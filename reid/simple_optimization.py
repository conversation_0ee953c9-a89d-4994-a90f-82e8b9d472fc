#!/usr/bin/env python3
"""
简单优化脚本
通过增加注册样本数量和阈值优化来提高识别准确率
"""

import torch
import torch.nn.functional as F
import json
import numpy as np
from pathlib import Path
import logging
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import random
from typing import Dict, List, Tuple, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model(model_path: str, device):
    """加载模型"""
    from feature_based_cat_recognition import FeatureExtractorModel
    
    checkpoint = torch.load(model_path, map_location=device)
    model = FeatureExtractorModel(feature_dim=2048)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model.to(device)
    
    return model

class SimpleOptimizer:
    """简单优化器"""
    
    def __init__(self, model, device, transform):
        self.model = model
        self.device = device
        self.transform = transform
        
    def extract_features(self, image_path: str) -> torch.Tensor:
        """提取单张图片的特征"""
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.transform(image).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            features = self.model(image_tensor)
        
        return features.squeeze(0)
    
    def build_enhanced_reference_features(self, annotations_path: str, images_dir: str, 
                                        samples_per_class: int = 100) -> Dict[str, torch.Tensor]:
        """构建增强的参考特征库"""
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        images_dir = Path(images_dir)
        reference_features = defaultdict(list)
        
        # 按类别收集图片
        class_images = defaultdict(list)
        for img_path, annotation_data in annotations.items():
            # 处理嵌套的标注格式
            if isinstance(annotation_data, dict):
                label = annotation_data.get('category', '小白')
            else:
                label = annotation_data

            full_path = images_dir / Path(img_path).name
            if full_path.exists():
                class_images[label].append(str(full_path))
        
        # 为每个类别提取特征
        for class_name, image_paths in class_images.items():
            logger.info(f"为类别 {class_name} 提取增强特征...")
            
            # 使用更多样本
            if len(image_paths) > samples_per_class:
                selected_paths = random.sample(image_paths, samples_per_class)
            else:
                selected_paths = image_paths * (samples_per_class // len(image_paths) + 1)
                selected_paths = selected_paths[:samples_per_class]
            
            for img_path in selected_paths:
                try:
                    features = self.extract_features(img_path)
                    reference_features[class_name].append(features)
                except Exception as e:
                    logger.warning(f"无法处理图片 {img_path}: {e}")
        
        # 计算每个类别的多个代表特征
        enhanced_features = {}
        for class_name, features_list in reference_features.items():
            if features_list:
                stacked_features = torch.stack(features_list)
                
                # 使用多种聚合方法
                mean_features = torch.mean(stacked_features, dim=0)
                median_features = torch.median(stacked_features, dim=0)[0]
                
                # 组合特征
                enhanced_features[class_name] = 0.7 * mean_features + 0.3 * median_features
                logger.info(f"类别 {class_name}: {len(features_list)} 个样本")
        
        return enhanced_features
    
    def predict_with_confidence(self, image_path: str, reference_features: Dict[str, torch.Tensor]) -> Tuple[str, float, Dict[str, float]]:
        """带置信度的预测"""
        query_features = self.extract_features(image_path)
        
        similarities = {}
        for class_name, ref_features in reference_features.items():
            similarity = F.cosine_similarity(query_features.unsqueeze(0), ref_features.unsqueeze(0)).item()
            similarities[class_name] = similarity
        
        # 找到最高相似度
        best_class = max(similarities, key=similarities.get)
        best_similarity = similarities[best_class]
        
        return best_class, best_similarity, similarities
    
    def optimize_thresholds(self, annotations_path: str, images_dir: str, 
                          reference_features: Dict[str, torch.Tensor]) -> float:
        """优化阈值"""
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        images_dir = Path(images_dir)
        
        # 收集验证数据
        validation_data = []
        for img_path, annotation_data in annotations.items():
            # 处理嵌套的标注格式
            if isinstance(annotation_data, dict):
                true_label = annotation_data.get('category', '小白')
            else:
                true_label = annotation_data

            full_path = images_dir / Path(img_path).name
            if full_path.exists():
                validation_data.append((str(full_path), true_label))
        
        # 随机选择验证集
        random.shuffle(validation_data)
        validation_data = validation_data[:500]  # 使用500个样本
        
        # 测试不同阈值
        thresholds = np.arange(0.6, 0.95, 0.02)
        best_threshold = 0.8
        best_accuracy = 0
        
        for threshold in thresholds:
            correct = 0
            total = 0
            
            for img_path, true_label in validation_data:
                try:
                    pred_label, similarity, similarities = self.predict_with_confidence(
                        img_path, reference_features
                    )
                    
                    if similarity >= threshold:
                        if pred_label == true_label:
                            correct += 1
                        total += 1
                        
                except Exception as e:
                    logger.warning(f"预测失败 {img_path}: {e}")
            
            if total > 0:
                accuracy = correct / total
                rejection_rate = 1 - (total / len(validation_data))
                
                logger.info(f"阈值 {threshold:.2f}: 准确率 {accuracy:.3f}, 拒绝率 {rejection_rate:.3f}")
                
                # 选择准确率最高且拒绝率合理的阈值
                if accuracy > best_accuracy and rejection_rate < 0.2:
                    best_accuracy = accuracy
                    best_threshold = threshold
        
        logger.info(f"最佳阈值: {best_threshold:.2f}, 准确率: {best_accuracy:.3f}")
        return best_threshold
    
    def comprehensive_test(self, annotations_path: str, images_dir: str, 
                          reference_features: Dict[str, torch.Tensor], 
                          threshold: float = 0.8, num_rounds: int = 10) -> Dict:
        """综合测试"""
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        images_dir = Path(images_dir)
        
        # 准备测试数据
        test_data = []
        for img_path, annotation_data in annotations.items():
            # 处理嵌套的标注格式
            if isinstance(annotation_data, dict):
                true_label = annotation_data.get('category', '小白')
            else:
                true_label = annotation_data

            full_path = images_dir / Path(img_path).name
            if full_path.exists():
                test_data.append((str(full_path), true_label))
        
        results = {
            'num_rounds': num_rounds,
            'threshold': threshold,
            'individual_results': [],
            'overall_stats': {}
        }
        
        all_accuracies = []
        category_stats = defaultdict(lambda: {'correct': 0, 'total': 0})
        all_errors = []
        
        for round_num in range(1, num_rounds + 1):
            logger.info(f"开始第 {round_num} 轮测试...")
            
            # 随机选择测试样本
            round_samples = random.sample(test_data, min(300, len(test_data)))
            
            correct_predictions = 0
            total_predictions = 0
            rejected = 0
            
            for img_path, true_label in round_samples:
                try:
                    pred_label, best_similarity, similarities = self.predict_with_confidence(
                        img_path, reference_features
                    )
                    
                    if best_similarity >= threshold:
                        total_predictions += 1
                        is_correct = (pred_label == true_label)
                        if is_correct:
                            correct_predictions += 1
                        else:
                            all_errors.append({
                                'round': round_num,
                                'image_path': img_path,
                                'true_category': true_label,
                                'predicted_category': pred_label,
                                'best_similarity': best_similarity
                            })
                        
                        # 更新类别统计
                        category_stats[true_label]['total'] += 1
                        if is_correct:
                            category_stats[true_label]['correct'] += 1
                    else:
                        rejected += 1
                        
                except Exception as e:
                    logger.warning(f"预测失败 {img_path}: {e}")
            
            # 计算指标
            if total_predictions > 0:
                accuracy = correct_predictions / total_predictions
                rejection_rate = rejected / len(round_samples)
                
                all_accuracies.append(accuracy)
                
                logger.info(f"第 {round_num} 轮: 准确率 {accuracy:.3f}, 拒绝率 {rejection_rate:.3f}")
        
        # 计算总体统计
        if all_accuracies:
            overall_accuracy = np.mean(all_accuracies)
            accuracy_std = np.std(all_accuracies)
            
            # 计算各类别准确率
            category_accuracies = {}
            for category, stats in category_stats.items():
                if stats['total'] > 0:
                    category_accuracies[category] = stats['correct'] / stats['total']
            
            results['overall_stats'] = {
                'average_accuracy': overall_accuracy,
                'accuracy_std': accuracy_std,
                'category_accuracies': category_accuracies,
                'total_errors': len(all_errors),
                'difficult_samples': self.identify_difficult_samples(all_errors)
            }
            
            logger.info(f"优化后总体结果:")
            logger.info(f"  平均准确率: {overall_accuracy:.3f} ± {accuracy_std:.3f}")
            logger.info(f"  各类别准确率: {category_accuracies}")
            logger.info(f"  总错误数: {len(all_errors)}")
        
        return results
    
    def identify_difficult_samples(self, errors: List[Dict]) -> List[str]:
        """识别困难样本"""
        error_count = defaultdict(int)
        for error in errors:
            image_name = Path(error['image_path']).name
            error_count[image_name] += 1
        
        difficult_samples = [img for img, count in error_count.items() if count >= 2]
        return difficult_samples

def main():
    """主函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据变换
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 数据路径
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    images_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    model_path = "feature_extractor_model.pth"
    
    # 加载模型
    model = load_model(model_path, device)
    optimizer = SimpleOptimizer(model, device, transform)
    
    # 构建增强的参考特征库
    logger.info("构建增强参考特征库...")
    reference_features = optimizer.build_enhanced_reference_features(
        annotations_path, images_dir, samples_per_class=100
    )
    
    # 优化阈值
    logger.info("优化阈值...")
    optimal_threshold = optimizer.optimize_thresholds(
        annotations_path, images_dir, reference_features
    )
    
    # 综合测试
    logger.info("进行综合测试...")
    results = optimizer.comprehensive_test(
        annotations_path, images_dir, reference_features, 
        threshold=optimal_threshold, num_rounds=10
    )
    
    # 保存结果
    with open('simple_optimization_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info("简单优化完成!")
    
    # 输出最终结果
    stats = results['overall_stats']
    print(f"\n🎯 优化结果总结:")
    print(f"平均准确率: {stats['average_accuracy']:.3f} ± {stats['accuracy_std']:.3f}")
    print(f"各类别准确率: {stats['category_accuracies']}")
    print(f"困难样本数量: {len(stats['difficult_samples'])}")
    print(f"困难样本: {stats['difficult_samples']}")

if __name__ == "__main__":
    main()
