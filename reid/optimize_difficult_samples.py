#!/usr/bin/env python3
"""
困难样本优化脚本
针对重复出错的图片和小花类别不稳定问题进行优化
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import json
import numpy as np
from pathlib import Path
import logging
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import random
from typing import Dict, List, Tuple
import timm

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DifficultyAwareDataset(Dataset):
    """困难样本感知数据集"""
    
    def __init__(self, annotations_path: str, images_dir: str, difficult_samples: List[str], 
                 transform=None, difficulty_weight: float = 3.0):
        with open(annotations_path, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        self.images_dir = Path(images_dir)
        self.transform = transform
        self.difficult_samples = set(Path(p).name for p in difficult_samples)
        self.difficulty_weight = difficulty_weight
        
        # 构建样本列表，困难样本重复采样
        self.samples = []
        for img_path, label in self.annotations.items():
            img_name = Path(img_path).name
            weight = self.difficulty_weight if img_name in self.difficult_samples else 1.0
            
            # 根据权重重复添加样本
            for _ in range(int(weight)):
                self.samples.append((img_path, label))
        
        logger.info(f"数据集构建完成: {len(self.samples)} 个样本 (困难样本权重: {difficulty_weight})")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]

        # 加载图片
        full_path = self.images_dir / Path(img_path).name
        image = Image.open(full_path).convert('RGB')

        if self.transform:
            image = self.transform(image)

        # 标签映射 - 处理可能的字典格式
        if isinstance(label, dict):
            # 如果label是字典，取第一个值
            label = list(label.values())[0] if label else '小白'

        label_map = {'小白': 0, '小花': 1, '小黑': 2}
        label_idx = label_map.get(label, 0)  # 使用get方法避免KeyError

        return image, label_idx

class EnhancedFeatureExtractor(nn.Module):
    """增强的特征提取器，专门针对困难样本优化"""

    def __init__(self, feature_dim: int = 2048, num_classes: int = 3):
        super().__init__()

        # 使用更大的骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )

        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]

        # 特征增强网络 - 与原始模型兼容的结构
        self.feature_enhancer = nn.Sequential(
            # 第一层：扩展特征
            nn.Linear(backbone_dim, feature_dim * 3),
            nn.BatchNorm1d(feature_dim * 3),
            nn.ReLU(),
            nn.Dropout(0.2),

            # 第二层：特征精炼
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.15),

            # 第三层：特征压缩
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),

            # 输出层：最终特征
            nn.Linear(feature_dim, feature_dim)
        )

        # 困难样本专用注意力机制
        self.difficulty_attention = nn.MultiheadAttention(
            embed_dim=feature_dim,
            num_heads=8,  # 与原始模型一致
            dropout=0.1,
            batch_first=True
        )

        # 类别特定的特征增强
        self.class_specific_enhancers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim, feature_dim),
                nn.BatchNorm1d(feature_dim),
                nn.ReLU(),
                nn.Linear(feature_dim, feature_dim)
            ) for _ in range(num_classes)
        ])

        self.feature_dim = feature_dim
        
    def forward(self, x, class_hint=None):
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # 困难样本注意力机制
        enhanced_features_reshaped = enhanced_features.unsqueeze(1)
        attended_features, attention_weights = self.difficulty_attention(
            enhanced_features_reshaped, 
            enhanced_features_reshaped, 
            enhanced_features_reshaped
        )
        attended_features = attended_features.squeeze(1)
        
        # 残差连接
        final_features = enhanced_features + attended_features
        
        # 类别特定增强（如果提供了类别提示）
        if class_hint is not None and final_features.size(0) > 1:  # 只在批量大小>1时使用
            batch_size = final_features.size(0)
            class_enhanced_features = torch.zeros_like(final_features)

            for i in range(batch_size):
                class_idx = class_hint[i].item()
                # 使用eval模式避免BatchNorm问题
                self.class_specific_enhancers[class_idx].eval()
                with torch.no_grad():
                    class_enhanced_features[i] = self.class_specific_enhancers[class_idx](final_features[i:i+1]).squeeze(0)

            final_features = final_features + 0.3 * class_enhanced_features
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(final_features, p=2, dim=1)
        
        return normalized_features, attention_weights

class DifficultyAwareLoss(nn.Module):
    """困难样本感知损失函数"""
    
    def __init__(self, margin: float = 0.5, difficulty_weight: float = 2.0):
        super().__init__()
        self.margin = margin
        self.difficulty_weight = difficulty_weight
        self.triplet_loss = nn.TripletMarginLoss(margin=margin, p=2)
        
    def forward(self, features, labels, is_difficult=None):
        batch_size = features.size(0)
        
        # 基础三元组损失
        triplet_loss = 0
        num_triplets = 0
        
        for i in range(batch_size):
            anchor = features[i:i+1]
            anchor_label = labels[i]
            
            # 寻找正样本和负样本
            positive_mask = (labels == anchor_label) & (torch.arange(batch_size) != i)
            negative_mask = (labels != anchor_label)
            
            if positive_mask.sum() > 0 and negative_mask.sum() > 0:
                # 随机选择正样本和负样本
                positive_idx = torch.where(positive_mask)[0]
                negative_idx = torch.where(negative_mask)[0]
                
                pos_idx = positive_idx[torch.randint(0, len(positive_idx), (1,))]
                neg_idx = negative_idx[torch.randint(0, len(negative_idx), (1,))]
                
                positive = features[pos_idx:pos_idx+1]
                negative = features[neg_idx:neg_idx+1]
                
                # 计算三元组损失
                loss = self.triplet_loss(anchor, positive, negative)
                
                # 如果是困难样本，增加权重
                if is_difficult is not None and is_difficult[i]:
                    loss *= self.difficulty_weight
                
                triplet_loss += loss
                num_triplets += 1
        
        if num_triplets > 0:
            triplet_loss /= num_triplets
        
        return triplet_loss

def load_difficult_samples(test_results_path: str) -> List[str]:
    """从测试结果中提取困难样本"""
    with open(test_results_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    difficult_samples = []
    image_error_count = defaultdict(int)
    
    # 统计每张图片的错误次数
    for round_result in results['individual_results']:
        for detail in round_result['detailed_results']:
            if not detail['is_correct']:
                image_name = Path(detail['image_path']).name
                image_error_count[image_name] += 1
    
    # 提取重复出错的图片
    for image_name, error_count in image_error_count.items():
        if error_count >= 2:  # 出错2次以上的图片
            difficult_samples.append(image_name)
    
    logger.info(f"识别出 {len(difficult_samples)} 个困难样本")
    return difficult_samples

def optimize_model():
    """优化模型以处理困难样本"""
    
    # 配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据路径
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    images_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    test_results_path = "comprehensive_test_results.json"
    model_path = "feature_extractor_model.pth"
    
    # 加载困难样本
    difficult_samples = load_difficult_samples(test_results_path)
    
    # 数据变换 - 增加数据增强
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomRotation(degrees=15),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 创建数据集
    dataset = DifficultyAwareDataset(
        annotations_path=annotations_path,
        images_dir=images_dir,
        difficult_samples=difficult_samples,
        transform=transform,
        difficulty_weight=3.0
    )
    
    dataloader = DataLoader(dataset, batch_size=16, shuffle=True, num_workers=4)
    
    # 加载现有模型
    logger.info("加载现有模型...")
    checkpoint = torch.load(model_path, map_location=device)
    
    # 创建增强模型
    model = EnhancedFeatureExtractor(feature_dim=2048, num_classes=3)
    
    # 尝试加载现有权重（部分加载）
    try:
        model_state = checkpoint['model_state_dict']
        model.load_state_dict(model_state, strict=False)
        logger.info("成功加载部分现有权重")
    except Exception as e:
        logger.warning(f"无法加载现有权重: {e}")
    
    model = model.to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=1e-5, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=50)
    criterion = DifficultyAwareLoss(margin=0.5, difficulty_weight=2.0)
    
    # 训练循环
    num_epochs = 20
    best_loss = float('inf')
    
    logger.info("开始困难样本优化训练...")
    
    for epoch in range(num_epochs):
        model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (images, labels) in enumerate(dataloader):
            images, labels = images.to(device), labels.to(device)
            
            # 标记困难样本
            is_difficult = torch.zeros(len(labels), dtype=torch.bool)
            # 这里可以根据实际情况标记困难样本
            
            optimizer.zero_grad()
            
            # 前向传播
            features, attention_weights = model(images, class_hint=labels)
            
            # 计算损失
            loss = criterion(features, labels, is_difficult)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, Loss: {loss.item():.4f}")
        
        scheduler.step()
        avg_loss = total_loss / num_batches
        
        logger.info(f"Epoch {epoch+1}/{num_epochs} 完成, 平均损失: {avg_loss:.4f}")
        
        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            
            # 保存优化后的模型
            optimized_model_path = "feature_extractor_model_optimized.pth"
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'feature_dim': 2048,
                'model_type': 'EnhancedFeatureExtractor',
                'difficult_samples': difficult_samples,
                'best_loss': best_loss,
                'epoch': epoch + 1
            }, optimized_model_path)
            
            logger.info(f"保存最佳模型: {optimized_model_path}")
    
    logger.info("困难样本优化训练完成!")
    return optimized_model_path

if __name__ == "__main__":
    optimized_model_path = optimize_model()
    print(f"优化完成! 模型保存在: {optimized_model_path}")
