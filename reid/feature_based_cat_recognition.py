#!/usr/bin/env python3
"""
基于特征提取的猫咪识别系统
通过三只猫咪学习强大的特征提取能力，然后泛化到任意猫咪
核心：特征提取 + 相似度匹配，而非分类
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple, Optional
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureExtractorModel(nn.Module):
    """强大的特征提取模型"""
    
    def __init__(self, feature_dim: int = 2048):
        super().__init__()
        
        # 使用适中的骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',  # 使用T版本节省内存
            pretrained=True,
            num_classes=0
        )   

        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)  # 标准输入尺寸
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 多层特征增强网络
        self.feature_enhancer = nn.Sequential(
            # 第一层：扩展特征
            nn.Linear(backbone_dim, feature_dim * 3),
            nn.BatchNorm1d(feature_dim * 3),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            # 第二层：特征精炼
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.15),
            
            # 第三层：特征压缩
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 输出层：最终特征
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=feature_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        self.feature_dim = feature_dim
        
        logger.info(f"特征提取模型初始化: {backbone_dim} -> {feature_dim}")
    
    def forward(self, x):
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # 注意力机制（自注意力）
        enhanced_features_reshaped = enhanced_features.unsqueeze(1)  # [B, 1, D]
        attended_features, _ = self.attention(
            enhanced_features_reshaped, 
            enhanced_features_reshaped, 
            enhanced_features_reshaped
        )
        attended_features = attended_features.squeeze(1)  # [B, D]
        
        # 残差连接
        final_features = enhanced_features + attended_features
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(final_features, p=2, dim=1)
        
        return normalized_features

class TripletDataset(Dataset):
    """三元组数据集用于特征学习"""
    
    def __init__(self, annotations_file: str, images_dir: str, train: bool = True, train_ratio: float = 0.85):
        self.annotations_file = annotations_file
        self.images_dir = images_dir
        self.train = train
        
        # 加载标注数据
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        # 过滤出三只主要猫咪
        self.valid_categories = ['小白', '小花', '小黑']
        self.category_images = defaultdict(list)
        
        for img_name, annotation in self.annotations.items():
            category = annotation.get('category', 'unknown')
            if category in self.valid_categories:
                img_path = os.path.join(images_dir, img_name)
                if os.path.exists(img_path):
                    self.category_images[category].append(img_path)
        
        # 分割训练和验证数据
        self.train_images = defaultdict(list)
        self.val_images = defaultdict(list)
        
        for category, images in self.category_images.items():
            random.shuffle(images)
            split_idx = int(len(images) * train_ratio)
            
            self.train_images[category] = images[:split_idx]
            self.val_images[category] = images[split_idx:]
        
        # 选择当前数据集
        if train:
            self.current_images = self.train_images
        else:
            self.current_images = self.val_images
        
        # 创建所有图片列表
        self.all_images = []
        self.image_to_category = {}
        
        for category, images in self.current_images.items():
            for img_path in images:
                self.all_images.append(img_path)
                self.image_to_category[img_path] = category
        
        # 强化的数据增强
        if train:
            self.transform = transforms.Compose([
                transforms.Resize((256, 256)),  # 标准尺寸
                transforms.RandomCrop((224, 224)),  # 匹配模型输入
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomRotation(degrees=25),
                transforms.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.4, hue=0.2),
                transforms.RandomGrayscale(p=0.1),
                transforms.RandomApply([transforms.GaussianBlur(3, sigma=(0.1, 2.0))], p=0.2),
                transforms.RandomApply([transforms.RandomAffine(degrees=0, translate=(0.1, 0.1))], p=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                   std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                   std=[0.229, 0.224, 0.225])
            ])
        
        logger.info(f"{'训练' if train else '验证'}数据集: {len(self.all_images)} 张图片")
        for category in self.valid_categories:
            count = len(self.current_images[category])
            logger.info(f"  {category}: {count} 张")
    
    def __len__(self):
        return len(self.all_images) * 3  # 每张图片生成3个三元组
    
    def __getitem__(self, idx):
        # 选择anchor图片
        anchor_idx = idx % len(self.all_images)
        anchor_path = self.all_images[anchor_idx]
        anchor_category = self.image_to_category[anchor_path]
        
        # 选择positive图片（同类别）
        positive_candidates = [img for img in self.current_images[anchor_category] if img != anchor_path]
        if positive_candidates:
            positive_path = random.choice(positive_candidates)
        else:
            positive_path = anchor_path  # 如果没有其他同类图片，使用自己
        
        # 选择negative图片（不同类别）
        negative_categories = [cat for cat in self.valid_categories if cat != anchor_category]
        negative_category = random.choice(negative_categories)
        negative_path = random.choice(self.current_images[negative_category])
        
        # 加载和变换图片
        anchor_image = self.transform(Image.open(anchor_path).convert('RGB'))
        positive_image = self.transform(Image.open(positive_path).convert('RGB'))
        negative_image = self.transform(Image.open(negative_path).convert('RGB'))
        
        return anchor_image, positive_image, negative_image

class AdvancedTripletLoss(nn.Module):
    """高级三元组损失"""
    
    def __init__(self, margin: float = 0.8, hard_mining: bool = True):
        super().__init__()
        self.margin = margin
        self.hard_mining = hard_mining
    
    def forward(self, anchor, positive, negative):
        # 计算距离
        pos_dist = torch.nn.functional.pairwise_distance(anchor, positive, p=2)
        neg_dist = torch.nn.functional.pairwise_distance(anchor, negative, p=2)
        
        # 基础三元组损失
        basic_loss = torch.relu(pos_dist - neg_dist + self.margin)
        
        if self.hard_mining:
            # 困难样本挖掘：关注损失最大的样本
            hard_samples = basic_loss > 0
            if hard_samples.sum() > 0:
                hard_loss = basic_loss[hard_samples]
                return hard_loss.mean()
            else:
                return basic_loss.mean()
        else:
            return basic_loss.mean()

class FeatureTrainer:
    """特征提取器训练器"""
    
    def __init__(self, annotations_file: str, images_dir: str):
        self.annotations_file = annotations_file
        self.images_dir = images_dir
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 创建数据集
        self.train_dataset = TripletDataset(annotations_file, images_dir, train=True, train_ratio=0.85)
        self.val_dataset = TripletDataset(annotations_file, images_dir, train=False, train_ratio=0.85)
        
        # 创建数据加载器
        self.train_loader = DataLoader(
            self.train_dataset,
            batch_size=4,  # 更小的批次大小节省内存
            shuffle=True,
            num_workers=2,
            pin_memory=False
        )

        self.val_loader = DataLoader(
            self.val_dataset,
            batch_size=4,
            shuffle=False,
            num_workers=2,
            pin_memory=False
        )
        
        # 创建模型
        self.model = FeatureExtractorModel(feature_dim=2048).to(self.device)
        
        # 损失函数
        self.criterion = AdvancedTripletLoss(margin=0.8, hard_mining=True)
        
        # 优化器
        self.optimizer = optim.AdamW([
            {'params': self.model.backbone.parameters(), 'lr': 1e-6},  # 极小的预训练模型学习率
            {'params': self.model.feature_enhancer.parameters(), 'lr': 1e-4},
            {'params': self.model.attention.parameters(), 'lr': 5e-5}
        ], weight_decay=1e-4)
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=80, eta_min=1e-8
        )
        
        self.training_history = []
        
        logger.info(f"特征训练器初始化完成")
        logger.info(f"训练样本: {len(self.train_dataset)}")
        logger.info(f"验证样本: {len(self.val_dataset)}")
    
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (anchor, positive, negative) in enumerate(self.train_loader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_features = self.model(anchor)
            positive_features = self.model(positive)
            negative_features = self.model(negative)
            
            # 计算损失
            loss = self.criterion(anchor_features, positive_features, negative_features)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 50 == 0:
                logger.info(f'Epoch {epoch}, Batch {batch_idx}: Loss={loss.item():.4f}')
        
        # 更新学习率
        self.scheduler.step()
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        return avg_loss
    
    def validate(self):
        """验证模型 - 通过相似度匹配测试"""
        self.model.eval()
        
        # 为每个类别构建特征库
        category_features = defaultdict(list)
        category_paths = defaultdict(list)
        
        with torch.no_grad():
            # 构建参考特征库（每个类别取前5张图片）
            for category in self.val_dataset.valid_categories:
                category_images = self.val_dataset.current_images[category][:5]
                
                for img_path in category_images:
                    image = Image.open(img_path).convert('RGB')
                    image_tensor = self.val_dataset.transform(image).unsqueeze(0).to(self.device)
                    features = self.model(image_tensor)
                    
                    category_features[category].append(features.cpu().numpy().flatten())
                    category_paths[category].append(img_path)
            
            # 测试剩余图片
            correct = 0
            total = 0
            
            for category in self.val_dataset.valid_categories:
                test_images = self.val_dataset.current_images[category][5:]  # 剩余图片用于测试
                
                for img_path in test_images:
                    image = Image.open(img_path).convert('RGB')
                    image_tensor = self.val_dataset.transform(image).unsqueeze(0).to(self.device)
                    query_features = self.model(image_tensor).cpu().numpy().flatten()
                    
                    # 与所有类别的特征库比较
                    best_similarity = -1.0
                    best_category = None
                    
                    for ref_category, ref_features_list in category_features.items():
                        for ref_features in ref_features_list:
                            similarity = np.dot(query_features, ref_features)
                            if similarity > best_similarity:
                                best_similarity = similarity
                                best_category = ref_category
                    
                    total += 1
                    if best_category == category:
                        correct += 1
        
        accuracy = 100.0 * correct / total if total > 0 else 0.0
        return accuracy
    
    def train(self, num_epochs: int = 80, save_path: str = 'feature_extractor_model.pth'):
        """完整训练过程"""
        logger.info(f"开始特征提取器训练: {num_epochs} epochs")
        
        best_accuracy = 0.0
        patience = 0
        max_patience = 15
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_loss = self.train_epoch(epoch + 1)
            
            # 验证
            val_accuracy = self.validate()
            
            epoch_time = time.time() - start_time
            
            # 记录历史
            epoch_result = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'val_accuracy': val_accuracy,
                'time': epoch_time
            }
            self.training_history.append(epoch_result)
            
            logger.info(f"Epoch {epoch + 1}/{num_epochs}:")
            logger.info(f"  训练损失: {train_loss:.4f}")
            logger.info(f"  验证准确率: {val_accuracy:.2f}%")
            logger.info(f"  用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if val_accuracy > best_accuracy:
                best_accuracy = val_accuracy
                self.save_model(save_path)
                logger.info(f"🎉 保存最佳模型: 验证准确率 {best_accuracy:.2f}%")
                patience = 0
            else:
                patience += 1
            
            # 早停
            if patience >= max_patience:
                logger.info(f"🛑 早停: 验证准确率已达到 {best_accuracy:.2f}%")
                break
            
            # 如果准确率达到100%，可以提前结束
            if val_accuracy >= 100.0:
                logger.info(f"🎯 达到完美准确率: {val_accuracy:.2f}%")
                break
        
        logger.info(f"🚀 特征提取器训练完成! 最佳验证准确率: {best_accuracy:.2f}%")
        
        # 保存训练历史
        history_path = save_path.replace('.pth', '_history.json')
        with open(history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2, default=str)
        
        return best_accuracy
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': self.model.feature_dim,
            'model_type': 'FeatureExtractorModel',
            'training_history': self.training_history
        }, save_path, _use_new_zipfile_serialization=False)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='基于特征提取的猫咪识别训练')
    parser.add_argument('--annotations', type=str, 
                       default='/home/<USER>/animsi/caby_training/tagging/annotations.json',
                       help='标注文件路径')
    parser.add_argument('--images', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails',
                       help='图片目录路径')
    parser.add_argument('--epochs', type=int, default=80, help='训练轮数')
    parser.add_argument('--output', type=str, default='feature_extractor_model.pth', help='输出模型路径')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.annotations):
        print(f"❌ 标注文件不存在: {args.annotations}")
        return
    
    if not os.path.exists(args.images):
        print(f"❌ 图片目录不存在: {args.images}")
        return
    
    # 创建训练器
    trainer = FeatureTrainer(args.annotations, args.images)
    
    # 开始训练
    best_accuracy = trainer.train(args.epochs, args.output)
    
    logger.info(f"🎉 基于特征提取的猫咪识别系统训练完成! 最佳准确率: {best_accuracy:.2f}%")
    logger.info(f"📁 模型已保存: {args.output}")

if __name__ == "__main__":
    main()
