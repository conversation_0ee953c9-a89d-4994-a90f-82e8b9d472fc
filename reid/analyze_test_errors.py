#!/usr/bin/env python3
"""
分析测试错误样本
"""

import json
import numpy as np
from collections import defaultdict

def analyze_errors(results_file: str):
    """分析错误样本"""
    
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print("="*80)
    print("🔍 错误样本详细分析")
    print("="*80)
    
    all_errors = []
    round_errors = defaultdict(list)
    category_errors = defaultdict(list)
    
    # 收集所有错误
    for round_idx, round_result in enumerate(results['individual_results']):
        round_num = round_result['round']
        
        for detail in round_result['detailed_results']:
            if not detail['is_correct']:
                error_info = {
                    'round': round_num,
                    'image_path': detail['image_path'],
                    'true_category': detail['true_category'],
                    'predicted_category': detail['predicted_category'],
                    'best_similarity': float(detail['best_similarity']),
                    'similarities': {k: float(v) for k, v in detail['similarities'].items()}
                }
                
                all_errors.append(error_info)
                round_errors[round_num].append(error_info)
                category_errors[detail['true_category']].append(error_info)
    
    print(f"\n📊 错误统计总览:")
    print(f"   总错误数量: {len(all_errors)}")
    print(f"   总测试数量: {sum(r['total_tested'] for r in results['individual_results'])}")
    print(f"   错误率: {len(all_errors) / sum(r['total_tested'] for r in results['individual_results']):.2%}")
    
    # 按轮次分析
    print(f"\n📋 各轮次错误分布:")
    for round_num in sorted(round_errors.keys()):
        errors = round_errors[round_num]
        total_in_round = next(r['total_tested'] for r in results['individual_results'] if r['round'] == round_num)
        print(f"   第{round_num}轮: {len(errors)} 个错误 / {total_in_round} 张图片 ({len(errors)/total_in_round:.2%})")
    
    # 按类别分析
    print(f"\n📋 各类别错误分析:")
    for category in sorted(category_errors.keys()):
        errors = category_errors[category]
        print(f"\n{category} 类别错误 ({len(errors)} 个):")
        
        # 统计错误类型
        error_types = defaultdict(int)
        confidences = []
        similarity_gaps = []
        
        for error in errors:
            predicted = error['predicted_category']
            confidence = error['best_similarity']
            error_types[predicted] += 1
            confidences.append(confidence)
            
            # 计算与正确类别的相似度差距
            true_sim = error['similarities'][error['true_category']]
            pred_sim = error['similarities'][predicted]
            similarity_gaps.append(pred_sim - true_sim)
            
            # 显示具体错误
            image_name = error['image_path'].split('/')[-1]
            print(f"  - {image_name}: 预测为{predicted} (相似度: {confidence:.3f})")
            print(f"    各类相似度: {error['similarities']}")
            print(f"    相似度差距: {pred_sim - true_sim:.3f}")
        
        print(f"  错误类型分布: {dict(error_types)}")
        if confidences:
            print(f"  错误样本平均置信度: {np.mean(confidences):.3f}")
            print(f"  平均相似度差距: {np.mean(similarity_gaps):.3f}")
    
    # 分析错误模式
    print(f"\n" + "="*80)
    print("🔍 错误模式分析")
    print("="*80)
    
    # 混淆矩阵
    confusion_matrix = defaultdict(lambda: defaultdict(int))
    for error in all_errors:
        confusion_matrix[error['true_category']][error['predicted_category']] += 1
    
    print(f"\n📊 混淆矩阵:")
    categories = ['小白', '小花', '小黑']
    header = "真实\\预测"
    print(f"{header:<10}", end="")
    for cat in categories:
        print(f"{cat:<8}", end="")
    print()
    
    for true_cat in categories:
        print(f"{true_cat:<10}", end="")
        for pred_cat in categories:
            count = confusion_matrix[true_cat][pred_cat]
            print(f"{count:<8}", end="")
        print()
    
    # 相似度分析
    print(f"\n📈 相似度分析:")
    all_confidences = [error['best_similarity'] for error in all_errors]
    all_gaps = []
    
    for error in all_errors:
        true_sim = error['similarities'][error['true_category']]
        pred_sim = error['similarities'][error['predicted_category']]
        all_gaps.append(pred_sim - true_sim)
    
    if all_confidences:
        print(f"   错误样本置信度范围: [{min(all_confidences):.3f}, {max(all_confidences):.3f}]")
        print(f"   错误样本平均置信度: {np.mean(all_confidences):.3f}")
        print(f"   相似度差距范围: [{min(all_gaps):.3f}, {max(all_gaps):.3f}]")
        print(f"   平均相似度差距: {np.mean(all_gaps):.3f}")
    
    # 问题图片分析
    print(f"\n🔍 问题图片特征:")
    
    # 找出重复出错的图片
    image_error_count = defaultdict(int)
    for error in all_errors:
        image_name = error['image_path'].split('/')[-1]
        image_error_count[image_name] += 1
    
    repeat_errors = {img: count for img, count in image_error_count.items() if count > 1}
    if repeat_errors:
        print(f"   重复出错的图片:")
        for img, count in sorted(repeat_errors.items(), key=lambda x: x[1], reverse=True):
            print(f"     {img}: {count} 次")
    else:
        print(f"   ✅ 没有重复出错的图片，错误是随机的")
    
    # 边界案例分析
    print(f"\n🎯 边界案例分析:")
    close_calls = [error for error in all_errors if error['best_similarity'] > 0.8]
    if close_calls:
        print(f"   高置信度错误 (>0.8): {len(close_calls)} 个")
        for error in close_calls[:3]:  # 显示前3个
            image_name = error['image_path'].split('/')[-1]
            print(f"     {image_name}: {error['true_category']} -> {error['predicted_category']} ({error['best_similarity']:.3f})")
    
    # 建议
    print(f"\n" + "="*80)
    print("💡 优化建议")
    print("="*80)
    
    error_rate = len(all_errors) / sum(r['total_tested'] for r in results['individual_results'])
    
    if error_rate < 0.005:  # <0.5%
        print("✅ 错误率很低，模型表现优秀")
    elif error_rate < 0.02:  # <2%
        print("⚠️ 存在少量错误，建议进一步优化")
    else:
        print("❌ 错误率较高，需要重点优化")
    
    print(f"\n具体建议:")
    
    # 基于错误模式给出建议
    if len(category_errors['小花']) > len(category_errors['小白']) + len(category_errors['小黑']):
        print("1. 小花类别错误较多，建议增加小花的训练数据")
    
    if any(gap < 0.1 for gap in all_gaps):
        print("2. 存在相似度差距很小的错误，建议提高特征区分度")
    
    if repeat_errors:
        print("3. 存在重复出错的图片，建议检查这些图片的质量")
    else:
        print("3. 错误是随机分布的，说明模型整体稳定")
    
    print("4. 考虑使用更大的注册样本数量")
    print("5. 可以设置相似度阈值来拒绝低置信度预测")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='分析测试错误')
    parser.add_argument('--results', type=str, default='comprehensive_test_results.json', 
                       help='测试结果文件')
    
    args = parser.parse_args()
    
    try:
        analyze_errors(args.results)
    except FileNotFoundError:
        print(f"❌ 结果文件不存在: {args.results}")
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
