#!/usr/bin/env python3
"""
原始三只猫咪的全面测试
扩大测试范围，充分随机测试，验证100%识别率的稳定性
同时测试系统吞吐性能
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple, Optional
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm
import statistics

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureExtractorModel(nn.Module):
    """特征提取模型"""
    
    def __init__(self, feature_dim: int = 2048):
        super().__init__()
        
        # 骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 多层特征增强网络
        self.feature_enhancer = nn.Sequential(
            # 第一层：扩展特征
            nn.Linear(backbone_dim, feature_dim * 3),
            nn.BatchNorm1d(feature_dim * 3),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            # 第二层：特征精炼
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.15),
            
            # 第三层：特征压缩
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 输出层：最终特征
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=feature_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        self.feature_dim = feature_dim
    
    def forward(self, x):
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # 注意力机制（自注意力）
        enhanced_features_reshaped = enhanced_features.unsqueeze(1)  # [B, 1, D]
        attended_features, _ = self.attention(
            enhanced_features_reshaped, 
            enhanced_features_reshaped, 
            enhanced_features_reshaped
        )
        attended_features = attended_features.squeeze(1)  # [B, D]
        
        # 残差连接
        final_features = enhanced_features + attended_features
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(final_features, p=2, dim=1)
        
        return normalized_features

class ComprehensiveTester:
    """全面测试器"""
    
    def __init__(self, model_path: str, annotations_file: str, images_dir: str):
        self.model_path = model_path
        self.annotations_file = annotations_file
        self.images_dir = images_dir
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 加载模型
        self.model = self._load_model()
        
        # 加载标注数据
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        # 准备三只猫咪的所有数据
        self.valid_categories = ['小白', '小花', '小黑']
        self.category_images = defaultdict(list)
        
        for img_name, annotation in self.annotations.items():
            category = annotation.get('category', 'unknown')
            if category in self.valid_categories:
                img_path = os.path.join(images_dir, img_name)
                if os.path.exists(img_path):
                    self.category_images[category].append(img_path)
        
        logger.info("全面测试器初始化完成")
        for category in self.valid_categories:
            logger.info(f"  {category}: {len(self.category_images[category])} 张图片")
    
    def _load_model(self):
        """加载模型"""
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 创建模型
        feature_dim = checkpoint.get('feature_dim', 2048)
        model = FeatureExtractorModel(feature_dim=feature_dim)
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        model.eval()
        
        logger.info(f"成功加载特征提取模型: 特征维度 {feature_dim}")
        return model
    
    def extract_features_batch(self, image_paths: List[str]) -> List[np.ndarray]:
        """批量提取特征"""
        features_list = []
        
        # 预处理所有图片
        images = []
        valid_paths = []
        
        for img_path in image_paths:
            try:
                image = Image.open(img_path).convert('RGB')
                image_tensor = self.transform(image)
                images.append(image_tensor)
                valid_paths.append(img_path)
            except Exception as e:
                logger.warning(f"图片加载失败 {img_path}: {e}")
                continue
        
        if not images:
            return []
        
        # 批量推理
        batch_tensor = torch.stack(images).to(self.device)
        
        with torch.no_grad():
            features = self.model(batch_tensor)
            features_np = features.cpu().numpy()
        
        return [feat for feat in features_np]
    
    def comprehensive_random_test(self, 
                                 register_samples: int = 10, 
                                 test_samples: int = 100,
                                 num_rounds: int = 5) -> Dict:
        """全面随机测试"""
        logger.info(f"开始全面随机测试: {num_rounds} 轮，每轮 {test_samples} 张测试图片")
        
        all_results = []
        
        for round_idx in range(num_rounds):
            logger.info(f"\n=== 第 {round_idx + 1} 轮测试 ===")
            
            # 为每个类别随机选择注册和测试图片
            category_features = {}
            category_test_images = {}
            
            for category in self.valid_categories:
                images = self.category_images[category].copy()
                random.shuffle(images)
                
                # 确保有足够的图片
                total_needed = register_samples + test_samples
                if len(images) < total_needed:
                    logger.warning(f"{category} 图片不足: {len(images)} < {total_needed}")
                    # 重复使用图片
                    images = images * ((total_needed // len(images)) + 1)
                    random.shuffle(images)
                
                # 分割注册和测试图片
                register_images = images[:register_samples]
                test_images = images[register_samples:register_samples + test_samples]
                
                # 提取注册特征
                register_features = self.extract_features_batch(register_images)
                if register_features:
                    category_features[category] = np.array(register_features)
                    category_test_images[category] = test_images
                    logger.info(f"  {category}: {len(register_features)} 个注册特征, {len(test_images)} 张测试图片")
            
            # 执行测试
            round_result = self._test_round(category_features, category_test_images, round_idx + 1)
            all_results.append(round_result)
        
        # 汇总结果
        return self._summarize_results(all_results)
    
    def _test_round(self, category_features: Dict, category_test_images: Dict, round_num: int) -> Dict:
        """执行一轮测试"""
        results = {
            'round': round_num,
            'total_tested': 0,
            'correct_predictions': 0,
            'category_results': {},
            'detailed_results': [],
            'performance_metrics': {}
        }
        
        # 性能计时
        start_time = time.time()
        feature_extraction_times = []
        similarity_computation_times = []
        
        for true_category in self.valid_categories:
            test_images = category_test_images[true_category]
            category_correct = 0
            category_total = 0
            category_details = []
            
            for img_path in test_images:
                # 计时：特征提取
                feat_start = time.time()
                query_features_list = self.extract_features_batch([img_path])
                feat_end = time.time()
                feature_extraction_times.append(feat_end - feat_start)
                
                if not query_features_list:
                    continue
                
                query_features = query_features_list[0]
                
                # 计时：相似度计算
                sim_start = time.time()
                best_similarity = -1.0
                best_category = None
                similarities_with_categories = {}
                
                for ref_category, ref_features_list in category_features.items():
                    max_sim_with_category = -1.0
                    for ref_features in ref_features_list:
                        similarity = np.dot(query_features, ref_features)
                        max_sim_with_category = max(max_sim_with_category, similarity)
                    
                    similarities_with_categories[ref_category] = max_sim_with_category
                    
                    if max_sim_with_category > best_similarity:
                        best_similarity = max_sim_with_category
                        best_category = ref_category
                
                sim_end = time.time()
                similarity_computation_times.append(sim_end - sim_start)
                
                category_total += 1
                results['total_tested'] += 1
                
                # 判断是否正确
                is_correct = best_category == true_category
                
                if is_correct:
                    category_correct += 1
                    results['correct_predictions'] += 1
                
                # 记录详细结果
                detail = {
                    'image_path': img_path,
                    'true_category': true_category,
                    'predicted_category': best_category,
                    'best_similarity': best_similarity,
                    'similarities': similarities_with_categories,
                    'is_correct': is_correct
                }
                category_details.append(detail)
                results['detailed_results'].append(detail)
            
            # 计算类别准确率
            category_accuracy = category_correct / category_total if category_total > 0 else 0.0
            results['category_results'][true_category] = {
                'accuracy': category_accuracy,
                'correct': category_correct,
                'total': category_total,
                'details': category_details
            }
            
            logger.info(f"  {true_category}: {category_accuracy:.1%} ({category_correct}/{category_total})")
        
        # 计算总体准确率
        overall_accuracy = results['correct_predictions'] / results['total_tested'] if results['total_tested'] > 0 else 0.0
        results['overall_accuracy'] = overall_accuracy
        
        # 性能指标
        end_time = time.time()
        total_time = end_time - start_time
        
        results['performance_metrics'] = {
            'total_time': total_time,
            'avg_time_per_image': total_time / results['total_tested'] if results['total_tested'] > 0 else 0,
            'throughput_images_per_second': results['total_tested'] / total_time if total_time > 0 else 0,
            'avg_feature_extraction_time': statistics.mean(feature_extraction_times) if feature_extraction_times else 0,
            'avg_similarity_computation_time': statistics.mean(similarity_computation_times) if similarity_computation_times else 0,
            'feature_extraction_times': feature_extraction_times,
            'similarity_computation_times': similarity_computation_times
        }
        
        logger.info(f"  第{round_num}轮准确率: {overall_accuracy:.1%}")
        logger.info(f"  处理时间: {total_time:.2f}s, 吞吐量: {results['performance_metrics']['throughput_images_per_second']:.2f} 图片/秒")
        
        return results
    
    def _summarize_results(self, all_results: List[Dict]) -> Dict:
        """汇总所有轮次的结果"""
        summary = {
            'num_rounds': len(all_results),
            'individual_results': all_results,
            'overall_statistics': {},
            'performance_statistics': {},
            'stability_analysis': {}
        }
        
        # 准确率统计
        accuracies = [r['overall_accuracy'] for r in all_results]
        category_accuracies = {cat: [] for cat in self.valid_categories}
        
        for result in all_results:
            for cat in self.valid_categories:
                if cat in result['category_results']:
                    category_accuracies[cat].append(result['category_results'][cat]['accuracy'])
        
        summary['overall_statistics'] = {
            'mean_accuracy': statistics.mean(accuracies),
            'std_accuracy': statistics.stdev(accuracies) if len(accuracies) > 1 else 0,
            'min_accuracy': min(accuracies),
            'max_accuracy': max(accuracies),
            'all_accuracies': accuracies,
            'category_statistics': {}
        }
        
        for cat in self.valid_categories:
            cat_accs = category_accuracies[cat]
            if cat_accs:
                summary['overall_statistics']['category_statistics'][cat] = {
                    'mean_accuracy': statistics.mean(cat_accs),
                    'std_accuracy': statistics.stdev(cat_accs) if len(cat_accs) > 1 else 0,
                    'min_accuracy': min(cat_accs),
                    'max_accuracy': max(cat_accs),
                    'all_accuracies': cat_accs
                }
        
        # 性能统计
        throughputs = [r['performance_metrics']['throughput_images_per_second'] for r in all_results]
        avg_times = [r['performance_metrics']['avg_time_per_image'] for r in all_results]
        
        summary['performance_statistics'] = {
            'mean_throughput': statistics.mean(throughputs),
            'std_throughput': statistics.stdev(throughputs) if len(throughputs) > 1 else 0,
            'max_throughput': max(throughputs),
            'min_throughput': min(throughputs),
            'mean_time_per_image': statistics.mean(avg_times),
            'std_time_per_image': statistics.stdev(avg_times) if len(avg_times) > 1 else 0
        }
        
        # 稳定性分析
        is_stable_100 = all(acc >= 0.999 for acc in accuracies)  # 99.9%以上认为是100%
        accuracy_range = max(accuracies) - min(accuracies)
        
        summary['stability_analysis'] = {
            'is_consistently_100_percent': is_stable_100,
            'accuracy_range': accuracy_range,
            'coefficient_of_variation': (statistics.stdev(accuracies) / statistics.mean(accuracies)) if statistics.mean(accuracies) > 0 else 0,
            'performance_stability': 'excellent' if accuracy_range < 0.02 else 'good' if accuracy_range < 0.05 else 'needs_improvement'
        }
        
        return summary

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='原始三只猫咪全面测试')
    parser.add_argument('--model', type=str, default='feature_extractor_model.pth', 
                       help='模型文件路径')
    parser.add_argument('--annotations', type=str, 
                       default='/home/<USER>/animsi/caby_training/tagging/annotations.json',
                       help='标注文件路径')
    parser.add_argument('--images', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails',
                       help='图片目录路径')
    parser.add_argument('--register-samples', type=int, default=15, help='每类注册样本数')
    parser.add_argument('--test-samples', type=int, default=150, help='每类测试样本数')
    parser.add_argument('--rounds', type=int, default=5, help='测试轮数')
    parser.add_argument('--output', type=str, default='comprehensive_test_results.json', help='输出文件')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    if not os.path.exists(args.annotations):
        print(f"❌ 标注文件不存在: {args.annotations}")
        return
    
    if not os.path.exists(args.images):
        print(f"❌ 图片目录不存在: {args.images}")
        return
    
    # 创建测试器
    tester = ComprehensiveTester(args.model, args.annotations, args.images)
    
    # 执行全面测试
    results = tester.comprehensive_random_test(
        register_samples=args.register_samples,
        test_samples=args.test_samples,
        num_rounds=args.rounds
    )
    
    # 保存结果
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    # 显示总结
    print("\n" + "="*80)
    print("🔍 原始三只猫咪全面测试结果")
    print("="*80)
    
    stats = results['overall_statistics']
    perf = results['performance_statistics']
    stability = results['stability_analysis']
    
    print(f"\n📊 准确率统计 ({results['num_rounds']} 轮测试):")
    print(f"   平均准确率: {stats['mean_accuracy']:.2%} ± {stats['std_accuracy']:.2%}")
    print(f"   准确率范围: [{stats['min_accuracy']:.2%}, {stats['max_accuracy']:.2%}]")
    print(f"   所有轮次: {[f'{acc:.1%}' for acc in stats['all_accuracies']]}")
    
    print(f"\n📊 各类别统计:")
    for cat, cat_stats in stats['category_statistics'].items():
        print(f"   {cat}: {cat_stats['mean_accuracy']:.2%} ± {cat_stats['std_accuracy']:.2%} "
              f"[{cat_stats['min_accuracy']:.2%}, {cat_stats['max_accuracy']:.2%}]")
    
    print(f"\n⚡ 性能统计:")
    print(f"   平均吞吐量: {perf['mean_throughput']:.2f} ± {perf['std_throughput']:.2f} 图片/秒")
    print(f"   吞吐量范围: [{perf['min_throughput']:.2f}, {perf['max_throughput']:.2f}] 图片/秒")
    print(f"   平均处理时间: {perf['mean_time_per_image']:.3f} ± {perf['std_time_per_image']:.3f} 秒/图片")
    
    print(f"\n🎯 稳定性分析:")
    print(f"   持续100%识别: {'✅' if stability['is_consistently_100_percent'] else '❌'}")
    print(f"   准确率波动范围: {stability['accuracy_range']:.2%}")
    print(f"   变异系数: {stability['coefficient_of_variation']:.4f}")
    print(f"   性能稳定性: {stability['performance_stability']}")
    
    print(f"\n🏆 总体评价:")
    if stability['is_consistently_100_percent']:
        print(f"   🎉 完美！所有轮次都达到了100%识别率")
        print(f"   ⚡ 系统吞吐量: {perf['mean_throughput']:.1f} 图片/秒")
        print(f"   ✅ 模型在大规模随机测试中表现稳定可靠")
    else:
        print(f"   ⚠️ 存在识别错误，需要进一步优化")
        print(f"   📈 平均准确率: {stats['mean_accuracy']:.2%}")
    
    print(f"\n📁 详细结果已保存: {args.output}")

if __name__ == "__main__":
    main()
