#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import logging
import random
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter

import torch
from torch.utils.data import Dataset, DataLoader, Sampler
import torchvision.transforms as transforms
from torchvision.transforms import functional as TF
from PIL import Image
import numpy as np

logger = logging.getLogger(__name__)

class CatReidDataset(Dataset):
    """猫个体识别数据集"""
    
    def __init__(
        self,
        annotations: List[Dict],
        images_dir: str,
        cat_to_id: Dict[str, int],
        split: str = 'train',
        image_size: Tuple[int, int] = (224, 224),
        augmentation_config: Optional[Dict] = None
    ):
        """
        Args:
            annotations: 标注数据列表
            images_dir: 图片目录
            cat_to_id: 猫名称到ID的映射
            split: 数据集分割 ('train', 'val', 'test')
            image_size: 图片尺寸
            augmentation_config: 数据增强配置
        """
        self.annotations = annotations
        self.images_dir = images_dir
        self.cat_to_id = cat_to_id
        self.split = split
        self.image_size = image_size
        
        # 创建变换
        self.transform = self._create_transforms(augmentation_config)
        
        # 统计信息
        cat_counts = Counter([ann['category'] for ann in annotations])
        logger.info(f"数据集 {split} 加载完成:")
        logger.info(f"  - 总样本数: {len(annotations)}")
        logger.info(f"  - 猫个体数: {len(cat_counts)}")
        for cat_name, count in cat_counts.items():
            logger.info(f"    {cat_name}: {count} 张")
    
    def _create_transforms(self, augmentation_config: Optional[Dict] = None):
        """创建数据变换"""
        if self.split == 'train' and augmentation_config:
            # 训练时的数据增强 - 降低增强强度
            aug_config = augmentation_config.get('train', {})
            normalize_config = augmentation_config.get('normalize', {})
            
            transform_list = [
                transforms.Resize(self.image_size),
            ]
            
            # 随机水平翻转 - 降低概率
            if aug_config.get('random_horizontal_flip', 0) > 0:
                transform_list.append(
                    transforms.RandomHorizontalFlip(p=min(0.3, aug_config['random_horizontal_flip']))
                )
            
            # 随机旋转 - 降低角度
            if aug_config.get('random_rotation', 0) > 0:
                transform_list.append(
                    transforms.RandomRotation(
                        degrees=min(10, aug_config['random_rotation']),
                        interpolation=transforms.InterpolationMode.BILINEAR
                    )
                )
            
            # 颜色抖动 - 降低强度
            if 'color_jitter' in aug_config:
                cj_config = aug_config['color_jitter']
                transform_list.append(
                    transforms.ColorJitter(
                        brightness=min(0.1, cj_config.get('brightness', 0)),
                        contrast=min(0.1, cj_config.get('contrast', 0)),
                        saturation=min(0.1, cj_config.get('saturation', 0)),
                        hue=min(0.05, cj_config.get('hue', 0))
                    )
                )
            
            # 先转换为Tensor
            transform_list.append(transforms.ToTensor())
            
            # 然后标准化 - 使用ImageNet标准
            transform_list.append(
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                )
            )
            
            # 随机擦除 - 降低概率和强度
            if aug_config.get('random_erasing', 0) > 0:
                transform_list.append(
                    transforms.RandomErasing(
                        p=min(0.1, aug_config['random_erasing']),
                        scale=(0.02, 0.1),  # 降低擦除区域
                        ratio=(0.3, 3.3)
                    )
                )
            
            return transforms.Compose(transform_list)
        
        else:
            # 验证/测试时的基础变换
            transform_list = [
                transforms.Resize(self.image_size),
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                )
            ]
            
            return transforms.Compose(transform_list)
    
    def __len__(self) -> int:
        return len(self.annotations)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int, Dict[str, Any]]:
        """
        获取单个样本
        
        Returns:
            image: 处理后的图像张量
            label: 类别标签
            metadata: 元数据字典
        """
        annotation = self.annotations[idx]
        
        # 加载图像
        image_path = os.path.join(self.images_dir, annotation['image_name'])
        
        try:
            image = Image.open(image_path).convert('RGB')
        except Exception as e:
            logger.error(f"无法加载图像 {image_path}: {e}")
            # 创建一个默认的黑色图像
            image = Image.new('RGB', self.image_size, color=(0, 0, 0))
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        # 获取标签
        cat_name = annotation['category']
        label = self.cat_to_id[cat_name]
        
        # 元数据 - 确保所有字段都有有效值
        metadata = {
            'image_name': annotation['image_name'],
            'cat_name': cat_name,
            'bbox': annotation.get('bbox', [0, 0, 0, 0]),  # 提供默认bbox
            'split': self.split,
            'user_id': annotation.get('user_id', ''),
            'timestamp': annotation.get('timestamp', '')
        }
        
        return image, label, metadata

class BalancedBatchSampler(Sampler):
    """平衡批次采样器 - 确保每个批次中包含所有类别"""
    
    def __init__(self, dataset: CatReidDataset, batch_size: int, samples_per_class: int = 8):
        """
        Args:
            dataset: 数据集
            batch_size: 批次大小
            samples_per_class: 每个类别的样本数
        """
        self.dataset = dataset
        self.batch_size = batch_size
        self.samples_per_class = samples_per_class
        
        # 按类别组织样本索引
        self.class_to_indices = defaultdict(list)
        for idx, (_, label, _) in enumerate(dataset):
            self.class_to_indices[label].append(idx)
        
        self.num_classes = len(self.class_to_indices)
        self.length = len(dataset) // batch_size
    
    def __iter__(self):
        for _ in range(self.length):
            batch_indices = []
            
            # 从每个类别中随机选择样本
            for class_id in self.class_to_indices:
                class_indices = self.class_to_indices[class_id]
                selected = random.choices(class_indices, k=self.samples_per_class)
                batch_indices.extend(selected)
            
            # 如果批次大小不够，随机填充
            while len(batch_indices) < self.batch_size:
                random_class = random.choice(list(self.class_to_indices.keys()))
                random_idx = random.choice(self.class_to_indices[random_class])
                batch_indices.append(random_idx)
            
            # 截断到批次大小
            batch_indices = batch_indices[:self.batch_size]
            random.shuffle(batch_indices)
            
            yield batch_indices
    
    def __len__(self):
        return self.length

def split_data(
    annotations: List[Dict], 
    train_ratio: float = 0.8, 
    random_state: int = 42
) -> Tuple[List[Dict], List[Dict], List[Dict]]:
    """分割数据集"""
    random.seed(random_state)
    
    # 按类别分组
    cat_annotations = defaultdict(list)
    for ann in annotations:
        cat_annotations[ann['category']].append(ann)
    
    train_annotations = []
    val_annotations = []
    test_annotations = []
    
    # 每个类别单独分割
    for cat_name, cat_anns in cat_annotations.items():
        random.shuffle(cat_anns)
        
        n_total = len(cat_anns)
        n_train = int(n_total * train_ratio)
        n_val = int(n_total * (1 - train_ratio) / 2)
        
        train_annotations.extend(cat_anns[:n_train])
        val_annotations.extend(cat_anns[n_train:n_train + n_val])
        test_annotations.extend(cat_anns[n_train + n_val:])
    
    # 重新打乱
    random.shuffle(train_annotations)
    random.shuffle(val_annotations)
    random.shuffle(test_annotations)
    
    return train_annotations, val_annotations, test_annotations

def create_data_loaders(
    annotations_path: str,
    images_dir: str,
    batch_size: int = 32,
    num_workers: int = 4,
    pin_memory: bool = True,
    train_ratio: float = 0.8,
    image_size: Tuple[int, int] = (224, 224),
    exclude_categories: Optional[List[str]] = None,
    augmentation_config: Optional[Dict] = None,
    use_balanced_sampler: bool = False
) -> Tuple[DataLoader, DataLoader, DataLoader, Dict[str, int], Dict[int, str]]:
    """
    创建数据加载器
    
    Returns:
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        test_loader: 测试数据加载器
        cat_to_id: 猫名称到ID的映射
        id_to_cat: ID到猫名称的映射
    """
    # 加载标注数据
    with open(annotations_path, 'r', encoding='utf-8') as f:
        annotations_dict = json.load(f)
    
    # 转换字典格式为列表格式
    all_annotations = []
    for image_name, annotation_data in annotations_dict.items():
        if isinstance(annotation_data, dict):
            # 处理字典格式的标注
            annotation = {
                'image_name': image_name,
                'category': annotation_data['category'],
                'user_id': annotation_data.get('user_id', ''),
                'timestamp': annotation_data.get('timestamp', '')
            }
        else:
            # 处理直接字符串格式的标注（兼容性）
            annotation = {
                'image_name': image_name,
                'category': str(annotation_data),
                'user_id': '',
                'timestamp': ''
            }
        all_annotations.append(annotation)
    
    # 过滤无效数据
    valid_annotations = []
    for ann in all_annotations:
        if exclude_categories and ann['category'] in exclude_categories:
            continue
        
        # 检查图像文件是否存在
        image_path = os.path.join(images_dir, ann['image_name'])
        if os.path.exists(image_path):
            valid_annotations.append(ann)
    
    logger.info(f"加载标注数据: {len(all_annotations)} 总数, {len(valid_annotations)} 有效")
    
    # 创建类别映射
    unique_cats = sorted(list(set(ann['category'] for ann in valid_annotations)))
    cat_to_id = {cat: idx for idx, cat in enumerate(unique_cats)}
    id_to_cat = {idx: cat for cat, idx in cat_to_id.items()}
    
    logger.info(f"检测到猫个体: {unique_cats}")
    
    # 分割数据
    train_annotations, val_annotations, test_annotations = split_data(
        valid_annotations, train_ratio=train_ratio
    )
    
    # 创建数据集
    train_dataset = CatReidDataset(
        train_annotations, images_dir, cat_to_id, 
        split='train', image_size=image_size,
        augmentation_config=augmentation_config
    )
    val_dataset = CatReidDataset(
        val_annotations, images_dir, cat_to_id, 
        split='val', image_size=image_size,
        augmentation_config=augmentation_config
    )
    test_dataset = CatReidDataset(
        test_annotations, images_dir, cat_to_id, 
        split='test', image_size=image_size,
        augmentation_config=augmentation_config
    )
    
    # 创建数据加载器
    train_sampler = None
    if use_balanced_sampler and len(unique_cats) > 1:
        train_sampler = BalancedBatchSampler(train_dataset, batch_size)
        train_shuffle = False
        train_batch_size = 1  # Sampler已经处理了批次
    else:
        train_shuffle = True
        train_batch_size = batch_size
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=train_batch_size,
        sampler=train_sampler,
        shuffle=train_shuffle,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    return train_loader, val_loader, test_loader, cat_to_id, id_to_cat

# 为了兼容性，保留旧的函数名
def create_dataloaders(*args, **kwargs):
    """兼容性函数"""
    return create_data_loaders(*args, **kwargs)

if __name__ == "__main__":
    # 测试代码
    print("测试猫个体识别数据集...")
    
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    images_dir = "/home/<USER>/animsi/caby_training/segment/renamed_thumbnails"
    
    # 测试数据加载器创建
    train_loader, val_loader, test_loader, cat_to_id, id_to_cat = create_data_loaders(
        annotations_path=annotations_path,
        images_dir=images_dir,
        batch_size=8,
        exclude_categories=["无"]
    )
    
    print(f"类别映射: {cat_to_id}")
    print(f"训练集大小: {len(train_loader.dataset)}")
    print(f"验证集大小: {len(val_loader.dataset)}")
    print(f"测试集大小: {len(test_loader.dataset)}")
    
    # 测试数据加载
    for images, labels, metadata in train_loader:
        print(f"批次形状: {images.shape}")
        print(f"标签: {labels}")
        break
    
    print("数据集测试完成！") 