# Quick Predict Calibrated - 使用说明

`quick_predict_calibrated.py` 是一个功能强大的猫咪识别预测脚本，支持单张预测、批量预测和完整数据集验证。

## 功能特性

- 🐱 **单张图片预测** - 快速预测单张猫咪图片
- 📦 **批量预测** - 批量处理多张图片
- 🔍 **数据集验证** - 完整验证数据集并与标注对比
- 📊 **详细统计** - 准确率、置信度、混淆矩阵等
- 🎯 **错误分析** - 识别错误预测案例
- ⚡ **高性能** - 平均速度 80+ images/s

## 基本用法

### 1. 单张图片预测

```bash
# 基本预测（只输出标签）
python quick_predict_calibrated.py image.jpg

# 包含置信度
python quick_predict_calibrated.py image.jpg --with-confidence

# 示例输出
小花 0.9915
```

### 2. 批量预测

```bash
# 预测目录下所有图片
python quick_predict_calibrated.py /path/to/images/ --batch

# 从文件列表预测
python quick_predict_calibrated.py image_list.txt --batch

# 包含置信度
python quick_predict_calibrated.py /path/to/images/ --batch --with-confidence

# 示例输出
2025-01-23_20-46-05_hls.jpg: 小花 0.9915
2025-01-23_21-57-41_hls.jpg: 小白 0.9822
2025-01-23_22-29-36_hls.jpg: 小黑 0.9991
```

### 3. 数据集验证

```bash
# 完整数据集验证（详细输出）
python quick_predict_calibrated.py --validate --verbose

# 简化输出
python quick_predict_calibrated.py --validate

# 示例输出（简化版）
准确率: 0.9954
错误数: 7
错误图片:
  2025-01-28_03-39-27_hls.jpg: 小花 -> 小白 (0.6871)
  2025-04-13_05-34-36_hls.jpg: 小白 -> 小花 (0.9547)
```

## 高级选项

### 命令行参数

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `input` | 输入图片路径、目录或'validate' | - |
| `--model, -m` | 校准模型路径 | `experiments/megadescriptor_3cats_stable/best_model_calibrated.pth` |
| `--verbose, -v` | 显示详细信息 | False |
| `--with-confidence` | 同时输出置信度 | False |
| `--batch` | 批量预测模式 | False |
| `--validate` | 验证完整数据集 | False |
| `--annotations` | 标注文件路径 | `/home/<USER>/animsi/caby_training/tagging/annotations.json` |
| `--images-dir` | 图片目录路径 | `/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails` |
| `--output, -o` | 输出结果到JSON文件 | - |

### 输出结果到文件

```bash
# 保存验证结果
python quick_predict_calibrated.py --validate -o validation_results.json

# 保存批量预测结果
python quick_predict_calibrated.py /path/to/images/ --batch -o batch_results.json
```

## 详细验证报告

使用 `--validate --verbose` 可以获得完整的验证报告：

```
============================================================
🐱 数据集验证报告
============================================================
📊 基本统计:
  总图片数: 1512
  找到图片: 1512
  缺失图片: 0
  成功预测: 1512
  失败预测: 0

🎯 准确率统计:
  总体准确率: 0.9954 (99.54%)
  正确预测: 1505
  错误预测: 7

📈 各类别准确率:
  小花: 0.9961 (99.61%) - 516/518
  小黑: 1.0000 (100.00%) - 394/394
  小白: 0.9917 (99.17%) - 595/600

📊 置信度统计:
  平均置信度: 0.9844
  正确预测平均置信度: 0.9849
  错误预测平均置信度: 0.8767
  高置信度比例 (≥90%): 98.88%
  中等置信度比例 (70-90%): 0.66%
  低置信度比例 (<70%): 0.46%

🔄 混淆矩阵:
    真实\预测      小花      小黑      小白
        小花     516       0       2
        小黑       0     394       0
        小白       2       3     595

❌ 错误预测案例 (共7个):
  [详细列出所有错误案例及其路径]
============================================================
```

## 性能指标

- **推理速度**: 平均 80+ images/s
- **准确率**: 99.54% (在完整数据集上)
- **置信度**: 平均 98.44%
- **高置信度比例**: 98.88% (≥90%)

## 使用场景

### 1. 开发调试
```bash
# 快速测试单张图片
python quick_predict_calibrated.py test_image.jpg --with-confidence
```

### 2. 批量处理
```bash
# 处理新收集的图片
python quick_predict_calibrated.py /new/images/ --batch -o results.json
```

### 3. 模型评估
```bash
# 完整数据集验证
python quick_predict_calibrated.py --validate --verbose -o evaluation.json
```

### 4. 生产部署前测试
```bash
# 简化输出，适合脚本调用
python quick_predict_calibrated.py --validate
```

## 注意事项

1. **模型路径**: 确保校准模型文件存在
2. **图片格式**: 支持 jpg, jpeg, png, bmp 格式
3. **内存使用**: 批量预测时模型只加载一次，内存效率高
4. **错误处理**: 自动处理损坏的图片，不会中断批量处理
5. **静默模式**: 非verbose模式下会抑制日志输出，适合脚本调用

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认模型文件未损坏

2. **图片加载失败**
   - 检查图片路径和格式
   - 确认图片文件完整

3. **标注文件错误**
   - 检查annotations.json路径
   - 确认JSON格式正确

### 调试模式
```bash
# 开启详细输出进行调试
python quick_predict_calibrated.py your_input --verbose
``` 