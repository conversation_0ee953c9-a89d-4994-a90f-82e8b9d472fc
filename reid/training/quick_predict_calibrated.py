#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
校准模型快速预测脚本
支持单张预测、批量预测和完整数据集验证
"""

import sys
import argparse
import json
import time
from pathlib import Path
import warnings
from typing import Dict, List, Tuple
from collections import Counter, defaultdict

# 抑制警告
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from predict_calibrated import CalibratedCatPredictor

def quick_predict_calibrated(image_path, model_path=None):
    """快速预测单张图像，只返回标签（使用校准模型）"""
    if model_path is None:
        model_path = "experiments/megadescriptor_3cats_stable/best_model_calibrated.pth"
    
    # 检查文件是否存在
    if not Path(image_path).exists():
        return "ERROR: 图片文件不存在"
    
    if not Path(model_path).exists():
        # 尝试原始模型
        original_model = model_path.replace('_calibrated.pth', '.pth')
        if Path(original_model).exists():
            model_path = original_model
        else:
            return "ERROR: 模型文件不存在"
    
    try:
        # 创建预测器（静默模式）
        predictor = CalibratedCatPredictor(model_path)
        
        # 重定向日志到 /dev/null（静默模式）
        import logging
        logging.getLogger().setLevel(logging.CRITICAL)
        
        # 加载模型
        predictor.load_model()
        predictor.setup_transform()
        
        # 进行预测（静默模式）
        # 临时禁用print
        import io
        import contextlib
        
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            result = predictor.predict(image_path, show_confidence=False, save_result=False)
        
        return result['predicted_cat']
        
    except Exception as e:
        return f"ERROR: {str(e)}"

def batch_predict(image_paths: List[str], model_path: str, verbose: bool = False) -> Dict[str, Dict]:
    """批量预测多张图片"""
    if not image_paths:
        return {}
    
    results = {}
    
    try:
        # 创建预测器
        predictor = CalibratedCatPredictor(model_path)
        
        if not verbose:
            import logging
            logging.getLogger().setLevel(logging.CRITICAL)
        
        # 加载模型
        predictor.load_model()
        predictor.setup_transform()
        
        # 批量预测
        if verbose:
            print(f"开始批量预测 {len(image_paths)} 张图片...")
        
        start_time = time.time()
        
        for i, image_path in enumerate(image_paths):
            if verbose and (i + 1) % 100 == 0:
                print(f"进度: {i + 1}/{len(image_paths)}")
            
            try:
                # 静默预测
                import io
                import contextlib
                f = io.StringIO()
                with contextlib.redirect_stdout(f):
                    result = predictor.predict(image_path, show_confidence=False, save_result=False)
                
                image_name = Path(image_path).name
                results[image_name] = {
                    'predicted_cat': result['predicted_cat'],
                    'confidence': result['confidence'],
                    'inference_time': result['inference_time'],
                    'status': 'success'
                }
                
            except Exception as e:
                image_name = Path(image_path).name
                results[image_name] = {
                    'predicted_cat': None,
                    'confidence': 0.0,
                    'inference_time': 0.0,
                    'status': 'error',
                    'error': str(e)
                }
        
        total_time = time.time() - start_time
        
        if verbose:
            successful = sum(1 for r in results.values() if r['status'] == 'success')
            failed = len(results) - successful
            print(f"批量预测完成: 成功 {successful} 张, 失败 {failed} 张")
            print(f"总耗时: {total_time:.2f}s, 平均速度: {len(image_paths)/total_time:.1f} images/s")
        
        return results
        
    except Exception as e:
        if verbose:
            print(f"批量预测失败: {e}")
        return {}

def validate_dataset(annotations_path: str, images_dir: str, model_path: str, verbose: bool = False) -> Dict:
    """验证完整数据集并与标注对比"""
    
    # 加载标注文件
    try:
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
    except Exception as e:
        return {'error': f"无法加载标注文件: {e}"}
    
    if verbose:
        print(f"加载标注文件: {len(annotations)} 个标注")
    
    # 过滤有效标注（排除"无"类别）
    valid_annotations = {
        image_name: info for image_name, info in annotations.items()
        if info.get('category') and info['category'] != '无'
    }
    
    if verbose:
        print(f"有效标注: {len(valid_annotations)} 个")
    
    # 构建图片路径列表
    images_dir = Path(images_dir)
    image_paths = []
    missing_images = []
    
    for image_name in valid_annotations.keys():
        image_path = images_dir / image_name
        if image_path.exists():
            image_paths.append(str(image_path))
        else:
            missing_images.append(image_name)
    
    if verbose:
        print(f"找到图片: {len(image_paths)} 张")
        if missing_images:
            print(f"缺失图片: {len(missing_images)} 张")
    
    # 批量预测
    prediction_results = batch_predict(image_paths, model_path, verbose)
    
    if not prediction_results:
        return {'error': '批量预测失败'}
    
    # 对比分析
    analysis = {
        'total_images': len(valid_annotations),
        'found_images': len(image_paths),
        'missing_images': len(missing_images),
        'successful_predictions': 0,
        'failed_predictions': 0,
        'correct_predictions': 0,
        'incorrect_predictions': 0,
        'accuracy': 0.0,
        'category_stats': defaultdict(lambda: {'total': 0, 'correct': 0, 'incorrect': 0}),
        'confidence_stats': {
            'high_confidence': 0,  # ≥0.9
            'medium_confidence': 0,  # 0.7-0.9
            'low_confidence': 0,  # <0.7
            'avg_confidence': 0.0,
            'avg_correct_confidence': 0.0,
            'avg_incorrect_confidence': 0.0
        },
        'error_cases': [],
        'missing_images_list': missing_images,
        'confusion_matrix': defaultdict(lambda: defaultdict(int))
    }
    
    confidences_all = []
    confidences_correct = []
    confidences_incorrect = []
    
    for image_name, pred_result in prediction_results.items():
        if pred_result['status'] == 'success':
            analysis['successful_predictions'] += 1
            
            # 获取真实标签
            true_label = valid_annotations[image_name]['category']
            predicted_label = pred_result['predicted_cat']
            confidence = pred_result['confidence']
            
            # 记录置信度
            confidences_all.append(confidence)
            
            # 置信度分类
            if confidence >= 0.9:
                analysis['confidence_stats']['high_confidence'] += 1
            elif confidence >= 0.7:
                analysis['confidence_stats']['medium_confidence'] += 1
            else:
                analysis['confidence_stats']['low_confidence'] += 1
            
            # 准确性分析
            analysis['category_stats'][true_label]['total'] += 1
            analysis['confusion_matrix'][true_label][predicted_label] += 1
            
            if predicted_label == true_label:
                analysis['correct_predictions'] += 1
                analysis['category_stats'][true_label]['correct'] += 1
                confidences_correct.append(confidence)
            else:
                analysis['incorrect_predictions'] += 1
                analysis['category_stats'][true_label]['incorrect'] += 1
                confidences_incorrect.append(confidence)
                
                # 记录错误案例
                analysis['error_cases'].append({
                    'image_name': image_name,
                    'true_label': true_label,
                    'predicted_label': predicted_label,
                    'confidence': confidence,
                    'image_path': str(images_dir / image_name)
                })
        else:
            analysis['failed_predictions'] += 1
    
    # 计算统计指标
    if analysis['successful_predictions'] > 0:
        analysis['accuracy'] = analysis['correct_predictions'] / analysis['successful_predictions']
        analysis['confidence_stats']['avg_confidence'] = sum(confidences_all) / len(confidences_all)
        
        if confidences_correct:
            analysis['confidence_stats']['avg_correct_confidence'] = sum(confidences_correct) / len(confidences_correct)
        
        if confidences_incorrect:
            analysis['confidence_stats']['avg_incorrect_confidence'] = sum(confidences_incorrect) / len(confidences_incorrect)
    
    return analysis

def print_validation_report(analysis: Dict):
    """打印验证报告"""
    print("\n" + "="*60)
    print("🐱 数据集验证报告")
    print("="*60)
    
    if 'error' in analysis:
        print(f"❌ 验证失败: {analysis['error']}")
        return
    
    # 基本统计
    print(f"📊 基本统计:")
    print(f"  总图片数: {analysis['total_images']}")
    print(f"  找到图片: {analysis['found_images']}")
    print(f"  缺失图片: {analysis['missing_images']}")
    print(f"  成功预测: {analysis['successful_predictions']}")
    print(f"  失败预测: {analysis['failed_predictions']}")
    
    # 准确率统计
    print(f"\n🎯 准确率统计:")
    print(f"  总体准确率: {analysis['accuracy']:.4f} ({analysis['accuracy']*100:.2f}%)")
    print(f"  正确预测: {analysis['correct_predictions']}")
    print(f"  错误预测: {analysis['incorrect_predictions']}")
    
    # 各类别准确率
    print(f"\n📈 各类别准确率:")
    for category, stats in analysis['category_stats'].items():
        if stats['total'] > 0:
            acc = stats['correct'] / stats['total']
            print(f"  {category}: {acc:.4f} ({acc*100:.2f}%) - {stats['correct']}/{stats['total']}")
    
    # 置信度统计
    conf_stats = analysis['confidence_stats']
    print(f"\n📊 置信度统计:")
    print(f"  平均置信度: {conf_stats['avg_confidence']:.4f}")
    print(f"  正确预测平均置信度: {conf_stats['avg_correct_confidence']:.4f}")
    if conf_stats['avg_incorrect_confidence'] > 0:
        print(f"  错误预测平均置信度: {conf_stats['avg_incorrect_confidence']:.4f}")
    
    total_preds = analysis['successful_predictions']
    if total_preds > 0:
        print(f"  高置信度比例 (≥90%): {conf_stats['high_confidence']/total_preds:.2%}")
        print(f"  中等置信度比例 (70-90%): {conf_stats['medium_confidence']/total_preds:.2%}")
        print(f"  低置信度比例 (<70%): {conf_stats['low_confidence']/total_preds:.2%}")
    
    # 混淆矩阵
    print(f"\n🔄 混淆矩阵:")
    categories = list(analysis['category_stats'].keys())
    if categories:
        print("    真实\\预测", end="")
        for cat in categories:
            print(f"  {cat:>6}", end="")
        print()
        
        for true_cat in categories:
            print(f"  {true_cat:>8}", end="")
            for pred_cat in categories:
                count = analysis['confusion_matrix'][true_cat][pred_cat]
                print(f"  {count:>6}", end="")
            print()
    
    # 错误案例
    if analysis['error_cases']:
        print(f"\n❌ 错误预测案例 (共{len(analysis['error_cases'])}个):")
        for i, error in enumerate(analysis['error_cases'][:10]):  # 只显示前10个
            print(f"  {i+1}. {error['image_name']}")
            print(f"     真实: {error['true_label']} | 预测: {error['predicted_label']} | 置信度: {error['confidence']:.4f}")
            print(f"     路径: {error['image_path']}")
        
        if len(analysis['error_cases']) > 10:
            print(f"     ... 还有 {len(analysis['error_cases']) - 10} 个错误案例")
    
    print("="*60)

def main():
    parser = argparse.ArgumentParser(description="校准模型快速预测和数据集验证")
    parser.add_argument("input", nargs="?", help="输入图片路径、图片目录或'validate'进行数据集验证")
    parser.add_argument("--model", "-m", 
                       default="experiments/megadescriptor_3cats_stable/best_model_calibrated.pth",
                       help="校准模型路径")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="显示详细信息")
    parser.add_argument("--with-confidence", action="store_true", 
                       help="同时输出置信度")
    parser.add_argument("--batch", action="store_true",
                       help="批量预测模式")
    parser.add_argument("--validate", action="store_true",
                       help="验证完整数据集")
    parser.add_argument("--annotations", 
                       default="/home/<USER>/animsi/caby_training/tagging/annotations.json",
                       help="标注文件路径")
    parser.add_argument("--images-dir",
                       default="/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails",
                       help="图片目录路径")
    parser.add_argument("--output", "-o", help="输出结果到JSON文件")
    
    args = parser.parse_args()
    
    # 静默模式：重定向stderr
    if not args.verbose:
        import os
        devnull = os.open(os.devnull, os.O_WRONLY)
        os.dup2(devnull, 2)
    
    # 数据集验证模式
    if args.validate or (args.input and args.input.lower() == 'validate'):
        if args.verbose:
            print("🔍 开始数据集验证...")
        
        analysis = validate_dataset(args.annotations, args.images_dir, args.model, args.verbose)
        
        if not args.verbose:
            # 简化输出
            if 'error' in analysis:
                print(f"ERROR: {analysis['error']}")
            else:
                print(f"准确率: {analysis['accuracy']:.4f}")
                print(f"错误数: {analysis['incorrect_predictions']}")
                if analysis['error_cases']:
                    print("错误图片:")
                    for error in analysis['error_cases']:
                        print(f"  {error['image_name']}: {error['true_label']} -> {error['predicted_label']} ({error['confidence']:.4f})")
        else:
            print_validation_report(analysis)
        
        # 保存结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)
            if args.verbose:
                print(f"\n结果保存至: {args.output}")
    
    # 批量预测模式
    elif args.batch:
        if not args.input:
            print("ERROR: 批量模式需要指定输入目录")
            return
        
        input_path = Path(args.input)
        if input_path.is_dir():
            # 目录模式：预测目录下所有图片
            image_paths = []
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                image_paths.extend(input_path.glob(ext))
            image_paths = [str(p) for p in image_paths]
        else:
            # 文件模式：从文件读取图片路径列表
            try:
                with open(args.input, 'r') as f:
                    image_paths = [line.strip() for line in f if line.strip()]
            except:
                print(f"ERROR: 无法读取文件 {args.input}")
                return
        
        if not image_paths:
            print("ERROR: 未找到图片文件")
            return
        
        results = batch_predict(image_paths, args.model, args.verbose)
        
        if not args.verbose:
            # 简化输出
            for image_name, result in results.items():
                if result['status'] == 'success':
                    if args.with_confidence:
                        print(f"{image_name}: {result['predicted_cat']} {result['confidence']:.4f}")
                    else:
                        print(f"{image_name}: {result['predicted_cat']}")
                else:
                    print(f"{image_name}: ERROR")
        
        # 保存结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            if args.verbose:
                print(f"结果保存至: {args.output}")
    
    # 单张预测模式
    else:
        if not args.input:
            print("ERROR: 需要指定输入图片路径")
            return
        
        # 进行预测
        if args.with_confidence:
            # 需要置信度信息
            try:
                predictor = CalibratedCatPredictor(args.model)
                if not args.verbose:
                    import logging
                    logging.getLogger().setLevel(logging.CRITICAL)
                
                predictor.load_model()
                predictor.setup_transform()
                
                # 静默预测
                import io
                import contextlib
                f = io.StringIO()
                with contextlib.redirect_stdout(f):
                    result = predictor.predict(args.input, show_confidence=False, save_result=False)
                
                print(f"{result['predicted_cat']} {result['confidence']:.4f}")
                
            except Exception as e:
                print(f"ERROR: {str(e)}")
        else:
            # 只需要标签
            result = quick_predict_calibrated(args.input, args.model)
            print(result)

if __name__ == "__main__":
    main() 