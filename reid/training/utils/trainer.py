#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import logging
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.optim import Optimizer
from torch.optim.lr_scheduler import _LRScheduler, CosineAnnealingLR, StepLR
import wandb
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# 为中文字体设置
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'Source Han Sans CN', 'WenQuanYi Zen Hei', 'DejaVu Sans', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class CosineAnnealingWarmupLR(_LRScheduler):
    """带预热的余弦退火学习率调度器"""
    
    def __init__(self, optimizer, warmup_epochs, total_epochs, min_lr=1e-6, last_epoch=-1):
        self.warmup_epochs = warmup_epochs
        self.total_epochs = total_epochs
        self.min_lr = min_lr
        super(CosineAnnealingWarmupLR, self).__init__(optimizer, last_epoch)
    
    def get_lr(self):
        if self.last_epoch < self.warmup_epochs:
            # Warmup阶段：线性增长
            return [base_lr * (self.last_epoch + 1) / self.warmup_epochs 
                   for base_lr in self.base_lrs]
        else:
            # 余弦退火阶段
            progress = (self.last_epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
            return [self.min_lr + (base_lr - self.min_lr) * 
                   (1 + np.cos(np.pi * progress)) / 2 
                   for base_lr in self.base_lrs]

class CatReidTrainer:
    """猫个体识别训练器"""
    
    def __init__(
        self,
        model: nn.Module,
        criterion: nn.Module,
        optimizer: Optimizer,
        device: torch.device,
        scheduler: Optional[_LRScheduler] = None,
        save_dir: str = "./experiments",
        experiment_name: str = "cat_reid",
        use_wandb: bool = True,
        wandb_project: str = "cat-reid",
        log_interval: int = 10,
        save_interval: int = 5
    ):
        """初始化训练器"""
        self.model = model
        self.criterion = criterion
        self.optimizer = optimizer
        self.device = device
        self.scheduler = scheduler
        
        # 创建保存目录
        self.save_dir = os.path.join(save_dir, experiment_name)
        os.makedirs(self.save_dir, exist_ok=True)
        
        self.log_interval = log_interval
        self.save_interval = save_interval
        
        # 初始化WandB
        self.use_wandb = use_wandb
        if use_wandb:
            wandb.init(
                project=wandb_project,
                name=experiment_name,
                config={
                    "model": model.__class__.__name__,
                    "optimizer": optimizer.__class__.__name__,
                    "scheduler": scheduler.__class__.__name__ if scheduler else "None",
                    "device": str(device)
                }
            )
            wandb.watch(model, log="all", log_freq=100)
        
        # 训练状态
        self.current_epoch = 0
        self.best_val_acc = 0.0
        self.train_history = []
        self.val_history = []
        
        logger.info(f"训练器初始化完成，保存目录: {self.save_dir}")
    
    def train_epoch(self, train_loader: DataLoader, cat_to_id: Dict[str, int]) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        cls_loss_total = 0.0
        triplet_loss_total = 0.0
        total_samples = 0
        correct_predictions = 0
        
        all_preds = []
        all_labels = []
        
        start_time = time.time()
        
        # 损失值监控
        batch_losses = []
        
        for batch_idx, (images, labels, metadata) in enumerate(train_loader):
            # 数据移到设备
            images = images.to(self.device)
            labels = labels.to(self.device)
            
            # 梯度清零
            self.optimizer.zero_grad()
            
            # 前向传播
            features, logits = self.model(images, return_features=True)
            
            # 计算损失
            loss_dict = self.criterion(features, logits, labels)
            total_batch_loss = loss_dict['total_loss']
            
            # 数值稳定性检查
            if torch.isnan(total_batch_loss) or torch.isinf(total_batch_loss):
                logger.warning(f"检测到异常损失值: {total_batch_loss.item()}, 跳过此批次")
                continue
            
            # 反向传播
            total_batch_loss.backward()
            
            # 梯度裁剪防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # 参数更新
            self.optimizer.step()
            
            # 统计
            batch_size = images.size(0)
            total_loss += total_batch_loss.item() * batch_size
            cls_loss_total += loss_dict['classification_loss'].item() * batch_size
            triplet_loss_total += loss_dict['triplet_loss'].item() * batch_size
            total_samples += batch_size
            
            # 记录批次损失用于监控
            batch_losses.append(total_batch_loss.item())
            
            # 准确率计算
            _, predicted = torch.max(logits, 1)
            correct_predictions += (predicted == labels).sum().item()
            
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            # 批次日志
            if batch_idx % self.log_interval == 0:
                logger.info(
                    f"训练 Epoch {self.current_epoch}, Batch {batch_idx}/{len(train_loader)}: "
                    f"Loss={total_batch_loss.item():.4f}, "
                    f"ClsLoss={loss_dict['classification_loss'].item():.4f}, "
                    f"TripletLoss={loss_dict['triplet_loss'].item():.4f}"
                )
                
                # 实时WandB日志
                if self.use_wandb:
                    wandb.log({
                        "batch_loss": total_batch_loss.item(),
                        "batch_cls_loss": loss_dict['classification_loss'].item(),
                        "batch_triplet_loss": loss_dict['triplet_loss'].item(),
                        "learning_rate": self.optimizer.param_groups[0]['lr']
                    })
        
        # 检查损失稳定性
        if len(batch_losses) > 10:
            loss_std = np.std(batch_losses[-10:])  # 最后10个批次的标准差
            loss_mean = np.mean(batch_losses[-10:])
            if loss_std / (loss_mean + 1e-8) > 0.5:  # 变异系数过大
                logger.warning(f"训练损失不稳定，变异系数: {loss_std / (loss_mean + 1e-8):.3f}")
        
        # 计算epoch统计
        epoch_time = time.time() - start_time
        avg_loss = total_loss / total_samples
        avg_cls_loss = cls_loss_total / total_samples
        avg_triplet_loss = triplet_loss_total / total_samples
        accuracy = correct_predictions / total_samples
        
        # 计算详细指标
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_labels, all_preds, average='macro', zero_division=0
        )
        
        epoch_metrics = {
            'loss': avg_loss,
            'classification_loss': avg_cls_loss,
            'triplet_loss': avg_triplet_loss,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'epoch_time': epoch_time,
            'loss_stability': np.std(batch_losses) / (np.mean(batch_losses) + 1e-8)
        }
        
        self.train_history.append(epoch_metrics)
        
        logger.info(
            f"训练 Epoch {self.current_epoch}: "
            f"Loss={avg_loss:.4f}, Acc={accuracy:.4f}, "
            f"F1={f1:.4f}, Time={epoch_time:.2f}s, "
            f"LossStability={epoch_metrics['loss_stability']:.3f}"
        )
        
        return epoch_metrics
    
    def validate_epoch(self, val_loader: DataLoader, cat_to_id: Dict[str, int]) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        cls_loss_total = 0.0
        triplet_loss_total = 0.0
        total_samples = 0
        correct_predictions = 0
        
        all_preds = []
        all_labels = []
        all_features = []
        
        with torch.no_grad():
            for images, labels, metadata in val_loader:
                # 数据移到设备
                images = images.to(self.device)
                labels = labels.to(self.device)
                
                # 前向传播
                features, logits = self.model(images, return_features=True)
                
                # 计算损失
                loss_dict = self.criterion(features, logits, labels)
                
                # 统计
                batch_size = images.size(0)
                total_loss += loss_dict['total_loss'].item() * batch_size
                cls_loss_total += loss_dict['classification_loss'].item() * batch_size
                triplet_loss_total += loss_dict['triplet_loss'].item() * batch_size
                total_samples += batch_size
                
                # 准确率计算
                _, predicted = torch.max(logits, 1)
                correct_predictions += (predicted == labels).sum().item()
                
                all_preds.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_features.append(features.cpu().numpy())
        
        # 计算epoch统计
        avg_loss = total_loss / total_samples
        avg_cls_loss = cls_loss_total / total_samples
        avg_triplet_loss = triplet_loss_total / total_samples
        accuracy = correct_predictions / total_samples
        
        # 计算详细指标
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_labels, all_preds, average='macro', zero_division=0
        )
        
        # 计算mAP（基于特征相似度）
        all_features = np.vstack(all_features)
        map_score = self._calculate_map(all_features, all_labels)
        
        epoch_metrics = {
            'loss': avg_loss,
            'classification_loss': avg_cls_loss,
            'triplet_loss': avg_triplet_loss,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'map': map_score
        }
        
        self.val_history.append(epoch_metrics)
        
        logger.info(
            f"验证 Epoch {self.current_epoch}: "
            f"Loss={avg_loss:.4f}, Acc={accuracy:.4f}, "
            f"F1={f1:.4f}, mAP={map_score:.4f}"
        )
        
        return epoch_metrics
    
    def _calculate_map(self, features: np.ndarray, labels: List) -> float:
        """计算mAP"""
        # 确保labels是numpy数组
        labels = np.array(labels)
        
        # 归一化特征
        features = features / (np.linalg.norm(features, axis=1, keepdims=True) + 1e-8)
        
        # 计算相似度矩阵
        similarity_matrix = np.dot(features, features.T)
        
        map_scores = []
        for i in range(len(labels)):
            query_label = labels[i]
            
            # 获取相似度排序（除了自己）
            similarities = similarity_matrix[i].copy()
            similarities[i] = -1  # 排除自己
            
            sorted_indices = np.argsort(similarities)[::-1]
            sorted_labels = labels[sorted_indices]
            
            # 计算AP
            relevant_items = (sorted_labels == query_label).astype(int)
            if relevant_items.sum() == 0:
                continue
            
            ap = 0.0
            num_relevant = 0
            for k, relevant in enumerate(relevant_items):
                if relevant:
                    num_relevant += 1
                    precision_at_k = num_relevant / (k + 1)
                    ap += precision_at_k
            
            ap /= relevant_items.sum()
            map_scores.append(ap)
        
        return np.mean(map_scores) if map_scores else 0.0
    
    def train(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        cat_to_id: Dict[str, int],
        id_to_cat: Dict[int, str],
        num_epochs: int
    ):
        """完整的训练流程"""
        logger.info(f"开始训练，共 {num_epochs} 个epochs")
        
        for epoch in range(num_epochs):
            self.current_epoch = epoch
            
            # 训练
            train_metrics = self.train_epoch(train_loader, cat_to_id)
            
            # 验证
            val_metrics = self.validate_epoch(val_loader, cat_to_id)
            
            # 学习率调度
            if self.scheduler:
                self.scheduler.step()
            
            # WandB日志
            if self.use_wandb:
                wandb.log({
                    "epoch": epoch,
                    "train/loss": train_metrics['loss'],
                    "train/accuracy": train_metrics['accuracy'],
                    "train/f1": train_metrics['f1'],
                    "val/loss": val_metrics['loss'],
                    "val/accuracy": val_metrics['accuracy'],
                    "val/f1": val_metrics['f1'],
                    "val/map": val_metrics['map']
                })
            
            # 保存最佳模型
            if val_metrics['accuracy'] > self.best_val_acc:
                self.best_val_acc = val_metrics['accuracy']
                self.save_checkpoint(epoch, "best_model.pth", val_metrics)
                logger.info(f"保存最佳模型，验证准确率: {self.best_val_acc:.4f}")
            
            # 定期保存
            if epoch % self.save_interval == 0:
                self.save_checkpoint(epoch, f"checkpoint_epoch_{epoch}.pth", val_metrics)
            
            # 生成混淆矩阵
            if epoch % 10 == 0:
                self.plot_confusion_matrix(val_loader, id_to_cat, epoch)
        
        # 训练完成后的清理
        self.save_training_history()
        if self.use_wandb:
            wandb.finish()
        
        logger.info("训练完成！")
    
    def save_checkpoint(self, epoch: int, filename: str, metrics: Dict[str, float]):
        """保存模型检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'metrics': metrics,
            'best_val_acc': self.best_val_acc
        }
        
        if self.scheduler:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        save_path = os.path.join(self.save_dir, filename)
        torch.save(checkpoint, save_path)
        logger.info(f"模型保存至: {save_path}")
    
    def plot_confusion_matrix(self, val_loader: DataLoader, id_to_cat: Dict[int, str], epoch: int):
        """绘制混淆矩阵"""
        self.model.eval()
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for images, labels, metadata in val_loader:
                images = images.to(self.device)
                logits = self.model(images)
                _, predicted = torch.max(logits, 1)
                
                all_preds.extend(predicted.cpu().numpy())
                all_labels.extend(labels.numpy())
        
        # 生成混淆矩阵
        cm = confusion_matrix(all_labels, all_preds)
        
        # 绘制
        plt.figure(figsize=(8, 6))
        cat_names = [id_to_cat[i] for i in range(len(id_to_cat))]
        sns.heatmap(cm, annot=True, fmt='d', xticklabels=cat_names, yticklabels=cat_names)
        plt.title(f'Confusion Matrix - Epoch {epoch}')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        
        # 保存图像
        save_path = os.path.join(self.save_dir, f'confusion_matrix_epoch_{epoch}.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        # 上传到WandB
        if self.use_wandb:
            wandb.log({f"confusion_matrix_epoch_{epoch}": wandb.Image(save_path)})
    
    def save_training_history(self):
        """保存训练历史"""
        import json
        
        history = {
            'train': self.train_history,
            'val': self.val_history
        }
        
        save_path = os.path.join(self.save_dir, 'training_history.json')
        with open(save_path, 'w') as f:
            json.dump(history, f, indent=2, default=str)
        
        logger.info(f"训练历史保存至: {save_path}")

def create_optimizer(
    model: nn.Module, 
    optimizer_type: str = "adamw", 
    lr: float = 1e-4, 
    weight_decay: float = 1e-4,
    betas: tuple = (0.9, 0.999),
    eps: float = 1e-8
):
    """创建优化器"""
    if optimizer_type.lower() == "adamw":
        return torch.optim.AdamW(
            model.parameters(), 
            lr=lr, 
            weight_decay=weight_decay,
            betas=betas,
            eps=eps
        )
    elif optimizer_type.lower() == "sgd":
        return torch.optim.SGD(model.parameters(), lr=lr, weight_decay=weight_decay, momentum=0.9)
    else:
        raise ValueError(f"Unsupported optimizer type: {optimizer_type}")

def create_scheduler(
    optimizer: Optimizer, 
    scheduler_type: str = "cosine", 
    num_epochs: int = 100,
    warmup_epochs: int = 5,
    min_lr: float = 1e-6
):
    """创建学习率调度器"""
    if scheduler_type.lower() == "cosine":
        return CosineAnnealingLR(optimizer, T_max=num_epochs, eta_min=min_lr)
    elif scheduler_type.lower() == "cosine_with_warmup":
        return CosineAnnealingWarmupLR(
            optimizer, 
            warmup_epochs=warmup_epochs, 
            total_epochs=num_epochs, 
            min_lr=min_lr
        )
    elif scheduler_type.lower() == "step":
        return StepLR(optimizer, step_size=num_epochs//3, gamma=0.1)
    elif scheduler_type.lower() == "none":
        return None
    else:
        raise ValueError(f"Unsupported scheduler type: {scheduler_type}")

if __name__ == "__main__":
    print("训练器模块测试通过！") 