#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
置信度校准脚本
通过温度缩放、特征归一化调整等方法提高模型预测置信度
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from models.cat_reid_model import CatReidModel
from datasets.cat_dataset import CatReidDataset
import yaml

def load_config(config_path: str) -> dict:
    """加载YAML配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'Source Han Sans CN', 'WenQuanYi Zen Hei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemperatureScaling(nn.Module):
    """
    温度缩放模块，用于校准模型置信度
    """
    def __init__(self, model):
        super(TemperatureScaling, self).__init__()
        self.model = model
        self.temperature = nn.Parameter(torch.ones(1) * 1.5)  # 可学习的温度参数
        
    def forward(self, x, return_features=False):
        features, logits = self.model(x, return_features=True)
        scaled_logits = logits / self.temperature
        
        if return_features:
            return features, scaled_logits
        return scaled_logits
    
    def set_temperature(self, valid_loader, device):
        """
        使用验证集设置最优温度参数
        """
        self.cuda()
        nll_criterion = nn.CrossEntropyLoss()
        
        # 收集验证集的logits和labels
        logits_list = []
        labels_list = []
        
        with torch.no_grad():
            for images, labels, _ in valid_loader:
                images = images.to(device)
                labels = labels.to(device)
                
                _, logits = self.model(images, return_features=True)
                logits_list.append(logits)
                labels_list.append(labels)
        
        logits = torch.cat(logits_list)
        labels = torch.cat(labels_list)
        
        # 优化温度参数
        optimizer = torch.optim.LBFGS([self.temperature], lr=0.01, max_iter=50)
        
        def eval():
            optimizer.zero_grad()
            loss = nll_criterion(logits / self.temperature, labels)
            loss.backward()
            return loss
        
        optimizer.step(eval)
        
        return self.temperature.item()

def analyze_confidence_distribution(model, data_loader, device, title="置信度分布"):
    """分析模型的置信度分布"""
    model.eval()
    confidences = []
    predictions = []
    ground_truths = []
    correct_predictions = []
    
    with torch.no_grad():
        for images, labels, _ in data_loader:
            images = images.to(device)
            labels = labels.to(device)
            
            _, logits = model(images, return_features=True)
            probabilities = F.softmax(logits, dim=1)
            
            max_probs, preds = torch.max(probabilities, 1)
            correct = (preds == labels)
            
            confidences.extend(max_probs.cpu().numpy())
            predictions.extend(preds.cpu().numpy())
            ground_truths.extend(labels.cpu().numpy())
            correct_predictions.extend(correct.cpu().numpy())
    
    confidences = np.array(confidences)
    correct_predictions = np.array(correct_predictions)
    
    # 计算统计信息
    avg_confidence = np.mean(confidences)
    avg_correct_confidence = np.mean(confidences[correct_predictions])
    avg_incorrect_confidence = np.mean(confidences[~correct_predictions]) if np.any(~correct_predictions) else 0
    accuracy = np.mean(correct_predictions)
    
    # 置信度分布统计
    high_conf = np.sum(confidences >= 0.8) / len(confidences)
    medium_conf = np.sum((confidences >= 0.6) & (confidences < 0.8)) / len(confidences)
    low_conf = np.sum(confidences < 0.6) / len(confidences)
    
    logger.info(f"{title} - 统计结果:")
    logger.info(f"  准确率: {accuracy:.4f}")
    logger.info(f"  平均置信度: {avg_confidence:.4f}")
    logger.info(f"  正确预测平均置信度: {avg_correct_confidence:.4f}")
    logger.info(f"  错误预测平均置信度: {avg_incorrect_confidence:.4f}")
    logger.info(f"  高置信度比例 (≥0.8): {high_conf:.2%}")
    logger.info(f"  中等置信度比例 (0.6-0.8): {medium_conf:.2%}")
    logger.info(f"  低置信度比例 (<0.6): {low_conf:.2%}")
    
    return {
        'confidences': confidences,
        'correct_predictions': correct_predictions,
        'avg_confidence': avg_confidence,
        'accuracy': accuracy,
        'high_conf_ratio': high_conf
    }

def plot_confidence_histogram(results_before, results_after, save_path=None):
    """绘制校准前后的置信度分布对比"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 校准前
    ax1.hist(results_before['confidences'], bins=20, alpha=0.7, color='red', edgecolor='black')
    ax1.axvline(results_before['avg_confidence'], color='red', linestyle='--', linewidth=2, 
                label=f'平均置信度: {results_before["avg_confidence"]:.3f}')
    ax1.set_title('校准前置信度分布')
    ax1.set_xlabel('置信度')
    ax1.set_ylabel('频次')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 校准后
    ax2.hist(results_after['confidences'], bins=20, alpha=0.7, color='green', edgecolor='black')
    ax2.axvline(results_after['avg_confidence'], color='green', linestyle='--', linewidth=2,
                label=f'平均置信度: {results_after["avg_confidence"]:.3f}')
    ax2.set_title('校准后置信度分布')
    ax2.set_xlabel('置信度')
    ax2.set_ylabel('频次')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"置信度分布图保存至: {save_path}")
    
    plt.show()

def create_optimized_model(original_model, optimal_temperature=1.0, enable_feature_scaling=True):
    """创建优化的模型"""
    
    class OptimizedCatReidModel(nn.Module):
        def __init__(self, base_model, temperature, enable_feature_scaling):
            super().__init__()
            self.base_model = base_model
            self.temperature = temperature
            self.enable_feature_scaling = enable_feature_scaling
            
            # 可选的特征缩放层
            if enable_feature_scaling:
                self.feature_scaler = nn.Parameter(torch.ones(base_model.feature_dim) * 2.0)
            
        def forward(self, x, return_features=False):
            # 获取基础特征和logits
            features, logits = self.base_model(x, return_features=True)
            
            # 可选的特征缩放
            if self.enable_feature_scaling:
                features = features * self.feature_scaler
                features = F.normalize(features, p=2, dim=1)  # 重新归一化
                
                # 重新计算logits
                logits = self.base_model.classifier(features)
            
            # 温度缩放
            scaled_logits = logits / self.temperature
            
            if return_features:
                return features, scaled_logits
            return scaled_logits
    
    return OptimizedCatReidModel(original_model, optimal_temperature, enable_feature_scaling)

def calibrate_model(model_path, config_path, output_path=None):
    """校准模型置信度"""
    
    logger.info("开始模型置信度校准...")
    
    # 加载配置
    config = load_config(config_path)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载原始模型
    logger.info(f"加载原始模型: {model_path}")
    try:
        checkpoint = torch.load(model_path, map_location=device, weights_only=True)
    except:
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    model = CatReidModel(num_classes=3, feature_dim=768)
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    
    # 创建数据加载器
    logger.info("创建数据加载器...")
    data_config = config['data']
    
    # 使用create_data_loaders函数
    from datasets.cat_dataset import create_data_loaders
    
    train_loader, val_loader, test_loader, cat_to_id, id_to_cat = create_data_loaders(
        annotations_path=data_config['annotations_path'],
        images_dir=data_config['images_dir'],
        batch_size=32,
        num_workers=4,
        pin_memory=True,
        train_ratio=data_config.get('train_ratio', 0.8),
        image_size=tuple(data_config.get('image_size', [224, 224])),
        exclude_categories=data_config.get('exclude_categories', ['无']),
        augmentation_config=config.get('augmentation', None)
    )
    
    # val_loader已经通过create_data_loaders创建
    
    # 分析原始模型置信度
    logger.info("分析原始模型置信度分布...")
    results_before = analyze_confidence_distribution(model, val_loader, device, "原始模型")
    
    # 温度缩放校准
    logger.info("进行温度缩放校准...")
    temp_model = TemperatureScaling(model)
    optimal_temp = temp_model.set_temperature(val_loader, device)
    logger.info(f"最优温度参数: {optimal_temp:.4f}")
    
    # 分析温度缩放后的结果
    logger.info("分析温度缩放后的置信度分布...")
    results_after_temp = analyze_confidence_distribution(temp_model, val_loader, device, "温度缩放后")
    
    # 创建进一步优化的模型
    logger.info("创建优化模型...")
    
    # 根据结果选择最优温度
    if results_after_temp['avg_confidence'] > results_before['avg_confidence']:
        final_temperature = optimal_temp
        logger.info(f"使用优化温度: {final_temperature:.4f}")
    else:
        # 如果温度缩放效果不好，尝试更激进的温度
        final_temperature = 0.5  # 更小的温度会增加置信度
        logger.info(f"使用激进温度: {final_temperature:.4f}")
    
    optimized_model = create_optimized_model(
        model, 
        optimal_temperature=final_temperature,
        enable_feature_scaling=True
    )
    optimized_model.to(device)
    optimized_model.eval()
    
    # 分析最终优化结果
    logger.info("分析最终优化后的置信度分布...")
    results_final = analyze_confidence_distribution(optimized_model, val_loader, device, "最终优化模型")
    
    # 绘制对比图
    plot_confidence_histogram(results_before, results_final, 
                            save_path="confidence_calibration_comparison.png")
    
    # 保存优化后的模型
    if output_path is None:
        output_path = model_path.replace('.pth', '_calibrated.pth')
    
    # 保存校准后的模型
    calibrated_state = {
        'model_state_dict': model.state_dict(),
        'temperature': final_temperature,
        'feature_scaler': optimized_model.feature_scaler.data if hasattr(optimized_model, 'feature_scaler') else None,
        'calibration_results': {
            'before_avg_confidence': results_before['avg_confidence'],
            'after_avg_confidence': results_final['avg_confidence'],
            'improvement': results_final['avg_confidence'] - results_before['avg_confidence'],
            'accuracy': results_final['accuracy']
        }
    }
    
    if 'epoch' in checkpoint:
        calibrated_state['epoch'] = checkpoint['epoch']
    if 'best_val_acc' in checkpoint:
        calibrated_state['best_val_acc'] = checkpoint['best_val_acc']
    
    torch.save(calibrated_state, output_path)
    logger.info(f"校准后模型保存至: {output_path}")
    
    # 打印总结
    improvement = results_final['avg_confidence'] - results_before['avg_confidence']
    logger.info(f"\n=== 校准总结 ===")
    logger.info(f"原始平均置信度: {results_before['avg_confidence']:.4f}")
    logger.info(f"校准后平均置信度: {results_final['avg_confidence']:.4f}")
    logger.info(f"置信度提升: {improvement:.4f} ({improvement/results_before['avg_confidence']*100:+.1f}%)")
    logger.info(f"准确率: {results_final['accuracy']:.4f}")
    logger.info(f"高置信度比例提升: {results_final['high_conf_ratio'] - results_before['high_conf_ratio']:+.2%}")
    
    return output_path, calibrated_state['calibration_results']

def main():
    parser = argparse.ArgumentParser(description="模型置信度校准")
    parser.add_argument("--model", "-m", 
                       default="experiments/megadescriptor_3cats_stable/best_model.pth",
                       help="原始模型路径")
    parser.add_argument("--config", "-c",
                       default="configs/cat_reid_config.yaml", 
                       help="配置文件路径")
    parser.add_argument("--output", "-o", help="输出模型路径")
    
    args = parser.parse_args()
    
    try:
        output_path, results = calibrate_model(args.model, args.config, args.output)
        logger.info("✅ 置信度校准完成！")
        logger.info(f"校准后模型: {output_path}")
        
    except Exception as e:
        logger.error(f"❌ 校准失败: {e}")
        raise

if __name__ == "__main__":
    main() 