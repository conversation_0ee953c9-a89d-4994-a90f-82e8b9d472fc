#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging
from pathlib import Path
import time

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'Source Han Sans CN', 'WenQuanYi Zen Hei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from models.cat_reid_model import CatReidModel
from datasets.cat_dataset import CatReidDataset
import torchvision.transforms as transforms

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CatPredictor:
    """猫个体识别预测器"""
    
    def __init__(self, model_path, device='cuda'):
        self.model_path = model_path
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.id_to_cat = None
        self.transform = None
        
    def load_model(self):
        """加载训练好的模型"""
        logger.info(f"加载模型: {self.model_path}")
        
        # 加载检查点（PyTorch 2.7兼容性）
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=True)
        except Exception:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 获取模型配置
        if 'model_config' in checkpoint:
            model_config = checkpoint['model_config']
            num_classes = model_config['num_classes']
            feature_dim = model_config['feature_dim']
        else:
            # 默认配置
            num_classes = 3  # 小白、小花、小黑
            feature_dim = 768
        
        # 创建模型
        self.model = CatReidModel(num_classes=num_classes, feature_dim=feature_dim)
        
        # 加载权重
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint)
            
        self.model.to(self.device)
        self.model.eval()
        
        # 设置类别映射
        self.id_to_cat = {0: '小白', 1: '小花', 2: '小黑'}
        
        logger.info(f"模型加载完成，类别数: {num_classes}")
        
        # 打印模型信息
        if 'epoch' in checkpoint:
            logger.info(f"训练轮数: {checkpoint['epoch']}")
        if 'best_val_acc' in checkpoint:
            logger.info(f"最佳验证准确率: {checkpoint['best_val_acc']:.4f}")
        if 'best_val_map' in checkpoint:
            logger.info(f"最佳验证mAP: {checkpoint['best_val_map']:.4f}")
    
    def setup_transform(self):
        """设置图像预处理"""
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),  # MegaDescriptor标准输入尺寸
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],  # ImageNet标准
                std=[0.229, 0.224, 0.225]
            )
        ])
        logger.info("图像预处理设置完成")
    
    def load_image(self, image_path):
        """加载和预处理图像"""
        image_path = Path(image_path)
        
        if not image_path.exists():
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        try:
            # 加载图像
            image = Image.open(image_path)
            
            # 转换为RGB（处理RGBA或灰度图）
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            logger.info(f"成功加载图像: {image_path}")
            logger.info(f"图像尺寸: {image.size}")
            
            return image
            
        except Exception as e:
            raise ValueError(f"无法加载图像 {image_path}: {e}")
    
    def predict(self, image_path, show_confidence=True, save_result=False):
        """预测单张图像"""
        # 加载图像
        image = self.load_image(image_path)
        original_image = image.copy()  # 保存原图用于可视化
        
        # 预处理
        input_tensor = self.transform(image).unsqueeze(0)  # 添加batch维度
        input_tensor = input_tensor.to(self.device)
        
        # 推理
        start_time = time.time()
        with torch.no_grad():
            features, logits = self.model(input_tensor, return_features=True)
            probabilities = F.softmax(logits, dim=1)
        inference_time = time.time() - start_time
        
        # 获取预测结果
        pred_id = torch.argmax(logits, dim=1).item()
        confidence = probabilities[0, pred_id].item()
        predicted_cat = self.id_to_cat[pred_id]
        
        # 获取所有类别的概率
        all_probs = probabilities[0].cpu().numpy()
        
        # 准备结果
        result = {
            'predicted_cat': predicted_cat,
            'confidence': confidence,
            'inference_time': inference_time,
            'all_probabilities': {
                self.id_to_cat[i]: float(all_probs[i]) 
                for i in range(len(all_probs))
            },
            'features': features.cpu().numpy()[0]  # 768维特征向量
        }
        
        # 打印结果
        self.print_result(image_path, result)
        
        # 可视化结果
        if show_confidence:
            self.visualize_result(original_image, image_path, result, save_result)
        
        return result
    
    def print_result(self, image_path, result):
        """打印预测结果"""
        print("\n" + "="*60)
        print("🐱 猫个体识别预测结果")
        print("="*60)
        print(f"📁 图片路径: {image_path}")
        print(f"🎯 预测结果: {result['predicted_cat']}")
        print(f"📊 置信度: {result['confidence']:.4f} ({result['confidence']*100:.2f}%)")
        print(f"⚡ 推理时间: {result['inference_time']*1000:.2f} ms")
        
        print(f"\n📈 所有类别概率:")
        for cat_name, prob in result['all_probabilities'].items():
            bar = "█" * int(prob * 20) + "░" * (20 - int(prob * 20))
            print(f"  {cat_name}: {prob:.4f} |{bar}|")
        
        # 置信度评级
        if result['confidence'] >= 0.8:
            grade = "🟢 高置信度"
        elif result['confidence'] >= 0.6:
            grade = "🟡 中等置信度"
        else:
            grade = "🔴 低置信度"
            
        print(f"\n🏅 置信度评级: {grade}")
        print("="*60)
    
    def visualize_result(self, image, image_path, result, save_result=False):
        """可视化预测结果"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 显示原图
        ax1.imshow(image)
        ax1.set_title(f"原图: {Path(image_path).name}")
        ax1.axis('off')
        
        # 添加预测标签
        predicted_cat = result['predicted_cat']
        confidence = result['confidence']
        
        # 根据置信度选择颜色
        if confidence >= 0.8:
            color = 'green'
        elif confidence >= 0.6:
            color = 'orange'
        else:
            color = 'red'
            
        ax1.text(10, 30, f"{predicted_cat}\n{confidence:.2%}", 
                bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
                fontsize=14, color='white', weight='bold')
        
        # 显示概率分布
        cats = list(result['all_probabilities'].keys())
        probs = list(result['all_probabilities'].values())
        
        bars = ax2.bar(cats, probs, color=['green' if cat == predicted_cat else 'lightblue' for cat in cats])
        ax2.set_title('各类别预测概率')
        ax2.set_ylabel('概率')
        ax2.set_ylim(0, 1)
        
        # 在柱状图上显示数值
        for bar, prob in zip(bars, probs):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{prob:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        if save_result:
            output_path = Path(image_path).parent / f"{Path(image_path).stem}_prediction.png"
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            logger.info(f"预测结果图保存至: {output_path}")
        
        plt.show()
    
    def predict_batch(self, image_paths, output_dir=None):
        """批量预测多张图像"""
        results = {}
        
        for image_path in image_paths:
            try:
                logger.info(f"处理图像: {image_path}")
                result = self.predict(image_path, show_confidence=False, save_result=False)
                results[str(image_path)] = result
            except Exception as e:
                logger.error(f"处理图像 {image_path} 失败: {e}")
                results[str(image_path)] = {'error': str(e)}
        
        # 保存批量结果
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            import json
            with open(output_dir / "batch_predictions.json", 'w', encoding='utf-8') as f:
                # 处理numpy数组序列化
                def convert_numpy(obj):
                    if isinstance(obj, np.ndarray):
                        return obj.tolist()
                    raise TypeError
                
                json.dump(results, f, ensure_ascii=False, indent=2, default=convert_numpy)
            
            logger.info(f"批量预测结果保存至: {output_dir}/batch_predictions.json")
        
        return results

def main():
    parser = argparse.ArgumentParser(description="猫个体识别单张图片预测")
    parser.add_argument("image", help="输入图片路径")
    parser.add_argument("--model", "-m", 
                       default="experiments/megadescriptor_3cats_stable/best_model.pth",
                       help="模型路径")
    parser.add_argument("--device", "-d", default="cuda", help="设备 (cuda/cpu)")
    parser.add_argument("--no-viz", action="store_true", help="不显示可视化结果")
    parser.add_argument("--save", "-s", action="store_true", help="保存预测结果图")
    parser.add_argument("--batch", nargs="+", help="批量预测多张图片")
    parser.add_argument("--output", "-o", help="批量预测输出目录")
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not Path(args.model).exists():
        logger.error(f"模型文件不存在: {args.model}")
        return
    
    # 创建预测器
    predictor = CatPredictor(args.model, args.device)
    
    try:
        # 加载模型
        predictor.load_model()
        predictor.setup_transform()
        
        if args.batch:
            # 批量预测
            logger.info(f"开始批量预测 {len(args.batch)} 张图片")
            results = predictor.predict_batch(args.batch, args.output)
            
            # 统计结果
            successful = sum(1 for r in results.values() if 'error' not in r)
            failed = len(results) - successful
            logger.info(f"批量预测完成: 成功 {successful} 张, 失败 {failed} 张")
            
        else:
            # 单张预测
            if not Path(args.image).exists():
                logger.error(f"图片文件不存在: {args.image}")
                return
            
            result = predictor.predict(
                args.image, 
                show_confidence=not args.no_viz,
                save_result=args.save
            )
            
            logger.info("✅ 预测完成")
            
    except Exception as e:
        logger.error(f"❌ 预测失败: {e}")
        raise

if __name__ == "__main__":
    main() 