#!/bin/bash

# 猫咪个体识别训练启动脚本
# 基于MegaDescriptor的猫咪识别模型训练

echo "🐱 开始猫咪个体识别模型训练..."

# 检查conda环境
if [[ "$CONDA_DEFAULT_ENV" != "caby_train" ]]; then
    echo "⚠️  请先激活conda环境: conda activate caby_train"
    exit 1
fi

# 检查配置文件
if [[ ! -f "configs/cat_reid_config.yaml" ]]; then
    echo "❌ 配置文件不存在: configs/cat_reid_config.yaml"
    exit 1
fi

# 检查标注文件
ANNOTATIONS_FILE="/home/<USER>/animsi/caby_training/tagging/annotations.json"
if [[ ! -f "$ANNOTATIONS_FILE" ]]; then
    echo "❌ 标注文件不存在: $ANNOTATIONS_FILE"
    exit 1
fi

# 检查图片目录
IMAGES_DIR="/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
if [[ ! -d "$IMAGES_DIR" ]]; then
    echo "❌ 图片目录不存在: $IMAGES_DIR"
    exit 1
fi

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

echo "📁 当前工作目录: $(pwd)"
echo "🐍 Python版本: $(python --version)"
echo "🔥 PyTorch版本: $(python -c 'import torch; print(torch.__version__)')"
echo "💾 CUDA可用: $(python -c 'import torch; print(torch.cuda.is_available())')"

# 开始训练
echo "🚀 开始训练..."
python train_cat_reid.py \
    --config configs/cat_reid_config.yaml \
    --seed 42

echo "✅ 训练完成！"
echo "📊 查看实验结果: experiments/megadescriptor_3cats_stable/" 