# 猫咪个体识别项目结构

本项目基于WildFusion的MegaDescriptor实现猫咪个体识别，经过清理后保留了最核心的训练和测试代码。

## 📁 项目结构

```
reid/training/
├── 📋 核心配置
│   ├── configs/
│   │   └── cat_reid_config.yaml          # 训练配置文件
│   └── requirements.txt                  # Python依赖包
│
├── 🧠 核心模块
│   ├── datasets/
│   │   ├── cat_dataset.py               # 数据集处理类
│   │   └── __init__.py
│   ├── models/
│   │   ├── cat_reid_model.py            # 模型定义（基于MegaDescriptor）
│   │   └── __init__.py
│   └── utils/
│       ├── trainer.py                   # 训练器类
│       └── __init__.py
│
├── 🚀 训练脚本
│   ├── train_cat_reid.py                # 主训练脚本
│   └── run_training.sh                  # 训练启动脚本
│
├── 🔍 测试与预测
│   ├── test_model.py                    # 模型测试和评估
│   ├── predict_calibrated.py           # 校准预测类
│   ├── quick_predict_calibrated.py     # 快速预测工具（支持批量和验证）
│   └── calibrate_confidence.py         # 置信度校准工具
│
├── 📊 实验结果
│   ├── experiments/
│   │   └── megadescriptor_3cats_stable/
│   │       ├── best_model.pth           # 最佳模型
│   │       ├── best_model_calibrated.pth # 校准后模型
│   │       └── training_history.json    # 训练历史
│   └── test_results_final/              # 最终测试结果
│
├── 📚 文档
│   ├── PROJECT_STRUCTURE.md             # 项目结构说明（本文件）
│   ├── QUICK_PREDICT_USAGE.md          # 预测工具使用说明
│   ├── CONFIDENCE_CALIBRATION_SUMMARY.md # 置信度校准说明
│   └── confidence_calibration_comparison.png # 校准对比图
│
└── 🔧 其他
    ├── .gitignore                       # Git忽略文件
    └── __init__.py                      # Python包初始化
```

## 🎯 核心功能模块

### 1. 训练模块
- **train_cat_reid.py**: 主训练脚本，整合了所有训练逻辑
- **run_training.sh**: 一键启动训练的shell脚本
- **cat_reid_config.yaml**: 完整的训练配置

### 2. 模型架构
- **cat_reid_model.py**: 基于MegaDescriptor-T-224的猫咪识别模型
- **cat_dataset.py**: 专门的猫咪数据集处理类
- **trainer.py**: 完整的训练器，包含损失函数、评估指标等

### 3. 预测工具
- **quick_predict_calibrated.py**: 多功能预测工具
  - 单张图片预测
  - 批量预测
  - 完整数据集验证
- **predict_calibrated.py**: 校准预测类
- **calibrate_confidence.py**: 置信度校准工具

### 4. 测试评估
- **test_model.py**: 综合模型测试脚本
- **test_results_final/**: 最终测试结果

## 🚀 快速开始

### 训练模型
```bash
cd training
bash run_training.sh
```

### 预测单张图片
```bash
python quick_predict_calibrated.py image.jpg --with-confidence
```

### 批量预测
```bash
python quick_predict_calibrated.py /path/to/images/ --batch --with-confidence
```

### 验证数据集
```bash
python quick_predict_calibrated.py --validate --verbose
```

### 模型测试
```bash
python test_model.py
```

## 📈 模型性能

- **准确率**: 99.54% (在1512张图片上)
- **各类别准确率**:
  - 小花: 99.61%
  - 小黑: 100.00%
  - 小白: 99.17%
- **推理速度**: 80+ images/s
- **置信度**: 平均98.44%

## 🛠️ 依赖环境

- Python 3.10
- PyTorch 2.7.1+cu126
- timm (用于MegaDescriptor)
- wandb (训练监控)
- scikit-learn (评估指标)
- matplotlib (可视化)

## 📝 重要文件说明

### 配置文件
- `cat_reid_config.yaml`: 包含所有训练参数，学习率、批次大小、损失权重等

### 模型文件
- `best_model.pth`: 未校准的最佳模型
- `best_model_calibrated.pth`: 置信度校准后的模型（推荐使用）

### 文档
- `QUICK_PREDICT_USAGE.md`: 详细的预测工具使用说明
- `CONFIDENCE_CALIBRATION_SUMMARY.md`: 置信度校准的技术说明

## 🔄 工作流程

1. **训练**: 使用 `train_cat_reid.py` 训练模型
2. **校准**: 使用 `calibrate_confidence.py` 校准置信度
3. **测试**: 使用 `test_model.py` 评估性能
4. **预测**: 使用 `quick_predict_calibrated.py` 进行预测

## 🗂️ 已清理的文件

为了保持项目清洁，以下调试和临时文件已被删除：
- 旧版本预测脚本
- 中间训练检查点
- 调试测试脚本
- 临时日志和缓存文件
- Jupyter notebook原型

现在的项目结构精简高效，包含了完整的训练、测试和部署所需的所有核心代码。 