#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging
from pathlib import Path
import json
import time
from datetime import datetime

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score
from sklearn.metrics import precision_recall_fscore_support
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'Source Han Sans CN', 'WenQuanYi Zen Hei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from models.cat_reid_model import CatReidModel
from datasets.cat_dataset import create_data_loaders
# 移除错误的导入

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_map(features: np.ndarray, labels: np.ndarray) -> float:
    """计算mAP (Mean Average Precision)"""
    try:
        from sklearn.metrics.pairwise import cosine_similarity
        
        n_samples = len(features)
        n_classes = len(np.unique(labels))
        
        if n_samples < 2:
            return 0.0
        
        # 计算特征相似度矩阵
        similarity_matrix = cosine_similarity(features)
        
        # 计算每个查询的AP
        aps = []
        for i in range(n_samples):
            query_label = labels[i]
            
            # 获取相似度分数（排除自己）
            similarities = similarity_matrix[i]
            similarities[i] = -1  # 排除自己
            
            # 按相似度排序
            ranked_indices = np.argsort(similarities)[::-1]
            ranked_labels = labels[ranked_indices]
            
            # 计算AP
            relevant_mask = (ranked_labels == query_label)
            if not np.any(relevant_mask):
                continue
                
            relevant_positions = np.where(relevant_mask)[0] + 1  # 1-indexed
            precision_at_k = np.arange(1, len(relevant_positions) + 1) / relevant_positions
            ap = np.mean(precision_at_k)
            aps.append(ap)
        
        return np.mean(aps) if aps else 0.0
        
    except Exception as e:
        logger.warning(f"mAP计算失败: {e}")
        return 0.0

class ModelTester:
    """模型测试器"""
    
    def __init__(self, model_path, config_path, device='cuda'):
        self.model_path = model_path
        self.config_path = config_path
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.test_loader = None
        self.cat_to_id = None
        self.id_to_cat = None
        
    def load_model(self):
        """加载训练好的模型"""
        logger.info(f"加载模型: {self.model_path}")
        
        # 加载检查点（PyTorch 2.7兼容性）
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=True)
        except Exception:
            # 如果weights_only=True失败，使用weights_only=False（仅限信任的文件）
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 获取模型配置
        if 'model_config' in checkpoint:
            model_config = checkpoint['model_config']
            num_classes = model_config['num_classes']
            feature_dim = model_config['feature_dim']
        else:
            # 从数据加载器推断
            _, _, _, cat_to_id, _ = create_data_loaders(
                annotations_path="/home/<USER>/animsi/caby_training/tagging/annotations.json",
                images_dir="/home/<USER>/animsi/caby_training/segment/renamed_thumbnails",
                batch_size=32,
                exclude_categories=["无"]
            )
            num_classes = len(cat_to_id)
            feature_dim = 768
        
        # 创建模型
        self.model = CatReidModel(num_classes=num_classes, feature_dim=feature_dim)
        
        # 加载权重
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint)
            
        self.model.to(self.device)
        self.model.eval()
        
        logger.info(f"模型加载完成，类别数: {num_classes}")
        
        # 打印模型信息
        if 'epoch' in checkpoint:
            logger.info(f"训练轮数: {checkpoint['epoch']}")
        if 'best_val_acc' in checkpoint:
            logger.info(f"最佳验证准确率: {checkpoint['best_val_acc']:.4f}")
        if 'best_val_map' in checkpoint:
            logger.info(f"最佳验证mAP: {checkpoint['best_val_map']:.4f}")
            
    def load_test_data(self):
        """加载测试数据"""
        logger.info("加载测试数据...")
        
        _, _, self.test_loader, self.cat_to_id, self.id_to_cat = create_data_loaders(
            annotations_path="/home/<USER>/animsi/caby_training/tagging/annotations.json",
            images_dir="/home/<USER>/animsi/caby_training/segment/renamed_thumbnails",
            batch_size=32,
            exclude_categories=["无"],
            augmentation_config=None  # 测试时不使用数据增强
        )
        
        logger.info(f"测试集大小: {len(self.test_loader.dataset)}")
        logger.info(f"猫个体: {list(self.id_to_cat.values())}")
        
    def run_inference(self):
        """在测试集上运行推理"""
        logger.info("开始测试集推理...")
        
        all_predictions = []
        all_labels = []
        all_features = []
        all_probabilities = []
        inference_times = []
        
        with torch.no_grad():
            for batch_idx, (images, labels, metadata) in enumerate(self.test_loader):
                images = images.to(self.device)
                labels = labels.to(self.device)
                
                # 计算推理时间
                start_time = time.time()
                features, logits = self.model(images, return_features=True)
                inference_time = time.time() - start_time
                inference_times.append(inference_time)
                
                # 获取预测
                probabilities = F.softmax(logits, dim=1)
                predictions = torch.argmax(logits, dim=1)
                
                # 收集结果
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_features.append(features.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
                
                if batch_idx % 10 == 0:
                    logger.info(f"处理批次 {batch_idx}/{len(self.test_loader)}")
        
        # 合并特征
        all_features = np.concatenate(all_features, axis=0)
        
        # 计算平均推理时间
        avg_inference_time = np.mean(inference_times)
        total_samples = len(all_predictions)
        fps = total_samples / sum(inference_times)
        
        logger.info(f"推理完成，平均推理时间: {avg_inference_time:.4f}s/batch")
        logger.info(f"吞吐量: {fps:.2f} samples/s")
        
        return {
            'predictions': np.array(all_predictions),
            'labels': np.array(all_labels), 
            'features': all_features,
            'probabilities': np.array(all_probabilities),
            'inference_time': avg_inference_time,
            'fps': fps
        }
    
    def calculate_metrics(self, results):
        """计算各种评估指标"""
        logger.info("计算评估指标...")
        
        predictions = results['predictions']
        labels = results['labels']
        probabilities = results['probabilities']
        features = results['features']
        
        # 基础分类指标
        accuracy = accuracy_score(labels, predictions)
        precision, recall, f1, support = precision_recall_fscore_support(
            labels, predictions, average='weighted'
        )
        
        # 每个类别的详细指标
        class_report = classification_report(
            labels, predictions, 
            target_names=[self.id_to_cat[i] for i in range(len(self.id_to_cat))],
            output_dict=True
        )
        
        # 混淆矩阵
        cm = confusion_matrix(labels, predictions)
        
        # 计算mAP
        map_score = calculate_map(features, labels)
        
        # 置信度分析
        max_probs = np.max(probabilities, axis=1)
        correct_mask = (predictions == labels)
        
        # 正确预测的置信度
        correct_confidence = max_probs[correct_mask]
        incorrect_confidence = max_probs[~correct_mask]
        
        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'map_score': map_score,
            'confusion_matrix': cm,
            'class_report': class_report,
            'confidence_stats': {
                'correct_mean': np.mean(correct_confidence) if len(correct_confidence) > 0 else 0,
                'correct_std': np.std(correct_confidence) if len(correct_confidence) > 0 else 0,
                'incorrect_mean': np.mean(incorrect_confidence) if len(incorrect_confidence) > 0 else 0,
                'incorrect_std': np.std(incorrect_confidence) if len(incorrect_confidence) > 0 else 0,
                'overall_mean': np.mean(max_probs),
                'overall_std': np.std(max_probs)
            },
            'per_class_accuracy': {
                self.id_to_cat[i]: class_report[self.id_to_cat[i]]['recall'] 
                for i in range(len(self.id_to_cat))
            }
        }
        
        return metrics
    
    def plot_confusion_matrix(self, cm, save_path):
        """绘制混淆矩阵"""
        plt.figure(figsize=(8, 6))
        
        # 计算百分比
        cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
        
        # 创建标签
        labels = [self.id_to_cat[i] for i in range(len(self.id_to_cat))]
        
        # 绘制热力图
        sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='Blues',
                   xticklabels=labels, yticklabels=labels,
                   cbar_kws={'label': '百分比 (%)'})
        
        plt.title('测试集混淆矩阵')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"混淆矩阵保存至: {save_path}")
    
    def plot_confidence_distribution(self, results, save_path):
        """绘制置信度分布"""
        predictions = results['predictions']
        labels = results['labels']
        probabilities = results['probabilities']
        
        max_probs = np.max(probabilities, axis=1)
        correct_mask = (predictions == labels)
        
        plt.figure(figsize=(12, 5))
        
        # 子图1: 总体置信度分布
        plt.subplot(1, 2, 1)
        plt.hist(max_probs[correct_mask], bins=20, alpha=0.7, label='正确预测', color='green')
        plt.hist(max_probs[~correct_mask], bins=20, alpha=0.7, label='错误预测', color='red')
        plt.xlabel('预测置信度')
        plt.ylabel('样本数量')
        plt.title('置信度分布对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 子图2: 每个类别的置信度
        plt.subplot(1, 2, 2)
        for class_id in range(len(self.id_to_cat)):
            class_mask = (labels == class_id)
            class_probs = max_probs[class_mask]
            if len(class_probs) > 0:
                plt.hist(class_probs, bins=15, alpha=0.6, 
                        label=self.id_to_cat[class_id])
        
        plt.xlabel('预测置信度')
        plt.ylabel('样本数量')
        plt.title('各类别置信度分布')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"置信度分布图保存至: {save_path}")
    
    def analyze_errors(self, results):
        """分析错误案例"""
        predictions = results['predictions']
        labels = results['labels']
        probabilities = results['probabilities']
        
        # 找出错误预测
        error_mask = (predictions != labels)
        error_indices = np.where(error_mask)[0]
        
        if len(error_indices) == 0:
            logger.info("🎉 完美预测！没有错误案例")
            return {}
        
        logger.info(f"发现 {len(error_indices)} 个错误预测")
        
        # 分析错误类型
        error_analysis = {}
        for idx in error_indices:
            true_label = labels[idx]
            pred_label = predictions[idx]
            confidence = np.max(probabilities[idx])
            
            true_cat = self.id_to_cat[true_label]
            pred_cat = self.id_to_cat[pred_label]
            error_type = f"{true_cat} -> {pred_cat}"
            
            if error_type not in error_analysis:
                error_analysis[error_type] = []
            
            error_analysis[error_type].append({
                'index': idx,
                'confidence': confidence,
                'true_label': true_cat,
                'pred_label': pred_cat
            })
        
        # 按错误类型排序
        sorted_errors = sorted(error_analysis.items(), 
                              key=lambda x: len(x[1]), reverse=True)
        
        logger.info("错误类型分析:")
        for error_type, errors in sorted_errors:
            avg_confidence = np.mean([e['confidence'] for e in errors])
            logger.info(f"  {error_type}: {len(errors)}个错误, 平均置信度: {avg_confidence:.3f}")
        
        return error_analysis
    
    def save_results(self, results, metrics, output_dir):
        """保存测试结果"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存数值结果
        results_dict = {
            'timestamp': datetime.now().isoformat(),
            'model_path': str(self.model_path),
            'test_samples': len(results['labels']),
            'performance': {
                'accuracy': float(metrics['accuracy']),
                'precision': float(metrics['precision']),
                'recall': float(metrics['recall']),
                'f1_score': float(metrics['f1_score']),
                'map_score': float(metrics['map_score'])
            },
            'inference': {
                'avg_time_per_batch': float(results['inference_time']),
                'fps': float(results['fps'])
            },
            'confidence_stats': {k: float(v) for k, v in metrics['confidence_stats'].items()},
            'per_class_accuracy': {k: float(v) for k, v in metrics['per_class_accuracy'].items()},
            'class_report': metrics['class_report']
        }
        
        # 保存为JSON
        json_path = output_dir / "test_results.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results_dict, f, ensure_ascii=False, indent=2)
        
        # 保存详细的分类报告
        report_path = output_dir / "classification_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=== 猫个体识别测试报告 ===\n\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"模型路径: {self.model_path}\n")
            f.write(f"测试样本数: {len(results['labels'])}\n\n")
            
            f.write("=== 整体性能 ===\n")
            f.write(f"准确率: {metrics['accuracy']:.4f}\n")
            f.write(f"精确率: {metrics['precision']:.4f}\n")
            f.write(f"召回率: {metrics['recall']:.4f}\n")
            f.write(f"F1分数: {metrics['f1_score']:.4f}\n")
            f.write(f"mAP: {metrics['map_score']:.4f}\n\n")
            
            f.write("=== 推理性能 ===\n")
            f.write(f"平均推理时间: {results['inference_time']:.4f}s/batch\n")
            f.write(f"吞吐量: {results['fps']:.2f} samples/s\n\n")
            
            f.write("=== 各类别性能 ===\n")
            for cat_name, accuracy in metrics['per_class_accuracy'].items():
                f.write(f"{cat_name}: {accuracy:.4f}\n")
            
            f.write("\n=== 详细分类报告 ===\n")
            from sklearn.metrics import classification_report
            detailed_report = classification_report(
                results['labels'], results['predictions'],
                target_names=[self.id_to_cat[i] for i in range(len(self.id_to_cat))]
            )
            f.write(detailed_report)
        
        logger.info(f"测试结果保存至: {output_dir}")
        return json_path
    
    def run_full_test(self, output_dir="test_results"):
        """运行完整测试流程"""
        logger.info("=== 开始模型测试 ===")
        
        # 1. 加载模型和数据
        self.load_model()
        self.load_test_data()
        
        # 2. 运行推理
        results = self.run_inference()
        
        # 3. 计算指标
        metrics = self.calculate_metrics(results)
        
        # 4. 打印结果
        self.print_summary(results, metrics)
        
        # 5. 生成可视化
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 混淆矩阵
        cm_path = output_dir / "confusion_matrix.png"
        self.plot_confusion_matrix(metrics['confusion_matrix'], cm_path)
        
        # 置信度分布
        conf_path = output_dir / "confidence_distribution.png"
        self.plot_confidence_distribution(results, conf_path)
        
        # 6. 错误分析
        error_analysis = self.analyze_errors(results)
        
        # 7. 保存结果
        results_path = self.save_results(results, metrics, output_dir)
        
        logger.info("=== 测试完成 ===")
        return results_path
    
    def print_summary(self, results, metrics):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("🐱 猫个体识别模型测试结果")
        print("="*60)
        
        print(f"📊 整体性能:")
        print(f"  准确率: {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)")
        print(f"  精确率: {metrics['precision']:.4f}")
        print(f"  召回率: {metrics['recall']:.4f}")
        print(f"  F1分数: {metrics['f1_score']:.4f}")
        print(f"  mAP:   {metrics['map_score']:.4f}")
        
        print(f"\n⚡ 推理性能:")
        print(f"  推理速度: {results['fps']:.2f} samples/s")
        print(f"  延迟:     {results['inference_time']*1000:.2f} ms/batch")
        
        print(f"\n🎯 各类别准确率:")
        for cat_name, accuracy in metrics['per_class_accuracy'].items():
            print(f"  {cat_name}: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        print(f"\n📈 置信度统计:")
        conf_stats = metrics['confidence_stats']
        print(f"  正确预测平均置信度: {conf_stats['correct_mean']:.3f}")
        print(f"  错误预测平均置信度: {conf_stats['incorrect_mean']:.3f}")
        print(f"  整体平均置信度:     {conf_stats['overall_mean']:.3f}")
        
        # 性能评级
        if metrics['accuracy'] >= 0.95:
            grade = "🏆 优秀"
        elif metrics['accuracy'] >= 0.90:
            grade = "🥈 良好"  
        elif metrics['accuracy'] >= 0.80:
            grade = "🥉 一般"
        else:
            grade = "❌ 需要改进"
            
        print(f"\n🏅 模型评级: {grade}")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description="猫个体识别模型测试")
    parser.add_argument("--model", "-m", required=True, help="模型路径")
    parser.add_argument("--config", "-c", default="configs/cat_reid_config.yaml", help="配置文件路径")
    parser.add_argument("--output", "-o", default="test_results", help="输出目录")
    parser.add_argument("--device", "-d", default="cuda", help="设备 (cuda/cpu)")
    
    args = parser.parse_args()
    
    # 检查模型文件是否存在
    if not Path(args.model).exists():
        logger.error(f"模型文件不存在: {args.model}")
        return
    
    # 创建测试器
    tester = ModelTester(args.model, args.config, args.device)
    
    # 运行测试
    try:
        results_path = tester.run_full_test(args.output)
        logger.info(f"✅ 测试成功完成，结果保存至: {results_path}")
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise

if __name__ == "__main__":
    main() 