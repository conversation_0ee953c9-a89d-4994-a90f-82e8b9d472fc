#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging
import random
import numpy as np
from pathlib import Path

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import yaml

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入项目模块
from datasets.cat_dataset import CatReidDataset, create_data_loaders
from models.cat_reid_model import CatReidModel, CombinedLoss, create_model
from utils.trainer import CatReidTrainer, create_optimizer, create_scheduler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def set_random_seed(seed: int):
    """设置随机种子以确保可重现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def load_config(config_path: str) -> dict:
    """加载YAML配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='猫个体识别训练')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--resume', type=str, default=None, help='恢复训练的检查点路径')
    args = parser.parse_args()
    
    # 设置随机种子
    set_random_seed(args.seed)
    logger.info(f"随机种子设置为: {args.seed}")
    
    # 加载配置
    logger.info(f"加载配置文件: {args.config}")
    config = load_config(args.config)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config['device']['use_cuda'] else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建数据加载器
    logger.info("创建数据加载器...")
    train_loader, val_loader, test_loader, cat_to_id, id_to_cat = create_data_loaders(
        annotations_path=config['data']['annotations_path'],
        images_dir=config['data']['images_dir'],
        batch_size=config['training']['batch_size'],
        num_workers=config['training']['num_workers'],
        pin_memory=config['training']['pin_memory'],
        train_ratio=config['data']['train_ratio'],
        image_size=tuple(config['data']['image_size']),
        exclude_categories=config['data']['exclude_categories']
    )
    
    num_classes = len(cat_to_id)
    logger.info(f"检测到 {num_classes} 只猫: {list(cat_to_id.keys())}")
    
    # 创建模型和损失函数
    logger.info("创建模型和损失函数...")
    model = create_model(
        num_classes=num_classes,
        backbone=config['model']['backbone'],
        feature_dim=config['model']['feature_dim'],
        dropout=config['model']['dropout'],
        pretrained=config['model']['pretrained'],
        freeze_backbone=config['model']['freeze_backbone']
    )
    model = model.to(device)
    
    # 创建损失函数
    criterion = CombinedLoss(
        classification_weight=float(config['loss']['classification_weight']),
        triplet_weight=float(config['loss']['triplet_weight']),
        triplet_margin=float(config['loss']['triplet_margin']),
        triplet_mining=config['loss']['triplet_mining'],
        label_smoothing=float(config['loss'].get('label_smoothing', 0.1))
    )
    
    # 创建优化器
    optimizer_config = config['training']['optimizer']
    optimizer = create_optimizer(
        model,
        optimizer_type=optimizer_config['type'],
        lr=float(optimizer_config['lr']),
        weight_decay=float(optimizer_config['weight_decay']),
        betas=tuple(optimizer_config.get('betas', (0.9, 0.999))),
        eps=float(optimizer_config.get('eps', 1e-8))
    )
    
    # 创建学习率调度器
    scheduler_config = config['training']['scheduler']
    scheduler = create_scheduler(
        optimizer,
        scheduler_type=scheduler_config['type'],
        num_epochs=config['training']['num_epochs'],
        warmup_epochs=scheduler_config.get('warmup_epochs', 5),
        min_lr=float(scheduler_config.get('min_lr', 1e-6))
    )
    
    # 创建训练器
    trainer = CatReidTrainer(
        model=model,
        criterion=criterion,
        optimizer=optimizer,
        device=device,
        scheduler=scheduler,
        save_dir=config['save']['save_dir'],
        experiment_name=config['logging']['experiment_name'],
        use_wandb=config['logging']['use_wandb'],
        wandb_project=config['logging']['wandb_project'],
        log_interval=config['logging']['log_interval'],
        save_interval=config['validation']['save_interval']
    )
    
    # 恢复训练（如果指定）
    start_epoch = 0
    if args.resume:
        logger.info(f"恢复训练从: {args.resume}")
        checkpoint = torch.load(args.resume, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if scheduler and 'scheduler_state_dict' in checkpoint:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        trainer.best_val_acc = checkpoint.get('best_val_acc', 0.0)
        logger.info(f"恢复训练从epoch {start_epoch}")
    
    # 开始训练
    logger.info("开始训练...")
    try:
        trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            cat_to_id=cat_to_id,
            id_to_cat=id_to_cat,
            num_epochs=config['training']['num_epochs']
        )
    except KeyboardInterrupt:
        logger.info("训练被用户中断")
        # 保存当前状态
        trainer.save_checkpoint(trainer.current_epoch, "interrupted_checkpoint.pth", {})
        logger.info("已保存中断时的检查点")
    
    logger.info("训练完成！")

if __name__ == "__main__":
    main() 