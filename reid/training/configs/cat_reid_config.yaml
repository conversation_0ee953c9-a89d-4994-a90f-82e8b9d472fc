# 猫个体识别训练配置

# 数据配置
data:
  annotations_path: "/home/<USER>/animsi/caby_training/tagging/annotations.json"
  images_dir: "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
  train_ratio: 0.8
  image_size: [224, 224]
  exclude_categories: ["无"]

# 模型配置
model:
  backbone: "hf-hub:BVRA/MegaDescriptor-T-224"
  feature_dim: 768
  dropout: 0.1
  pretrained: true
  freeze_backbone: false  # 是否冻结骨干网络

# 损失函数配置 - 修复损失过大问题
loss:
  classification_weight: 0.01  # 进一步大幅降低分类损失权重
  triplet_weight: 1.0  # 保持triplet损失权重
  triplet_margin: 0.3  # 降低margin
  triplet_mining: "batch_hard"  # batch_all or batch_hard
  label_smoothing: 0.1  # 标签平滑，提高泛化性

# 训练配置 - 修复损失过大问题
training:
  num_epochs: 100
  batch_size: 32
  num_workers: 4
  pin_memory: true
  
  # 优化器配置 - 进一步降低学习率
  optimizer:
    type: "adamw"  # adamw or sgd
    lr: 1e-5  # 大幅降低学习率
    weight_decay: 1e-4  # 降低权重衰减
    betas: [0.9, 0.999]  # Adam参数
    eps: 1e-8
  
  # 学习率调度器 - 添加warmup
  scheduler:
    type: "cosine_with_warmup"  # cosine, step, cosine_with_warmup or none
    warmup_epochs: 10  # 增加warmup轮数
    min_lr: 1e-7  # 更小的最小学习率
    
  # 早停配置
  early_stopping:
    patience: 20  # 增加patience
    min_delta: 0.001

# 验证配置
validation:
  eval_interval: 1  # 每隔多少个epoch进行验证
  save_interval: 5  # 每隔多少个epoch保存模型

# 日志配置
logging:
  log_interval: 10  # batch日志打印间隔
  use_wandb: true
  wandb_project: "cat-reid"
  experiment_name: "megadescriptor_3cats_stable"

# 设备配置
device:
  use_cuda: true
  gpu_ids: [0]  # 使用的GPU ID列表

# 保存配置
save:
  save_dir: "/home/<USER>/animsi/caby_training/reid/training/experiments"
  save_best_only: false
  save_checkpoints: true

# 数据增强配置 - 新增
augmentation:
  train:
    random_horizontal_flip: 0.5
    random_rotation: 15
    color_jitter:
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.1
    random_erasing: 0.3
    gaussian_blur: 0.1
  
  normalize:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225] 