# 🐱 猫咪个体识别系统

基于WildFusion MegaDescriptor的高精度猫咪个体识别系统，支持三只猫咪（小白、小花、小黑）的自动识别。

## ✨ 特性

- 🎯 **高精度识别**: 准确率达99.54%
- ⚡ **高性能推理**: 80+ images/s推理速度
- 🔧 **置信度校准**: 支持置信度校准，提高预测可靠性
- 📦 **批量处理**: 支持单张、批量和数据集验证
- 📊 **详细分析**: 混淆矩阵、置信度分布、错误分析
- 🚀 **易于部署**: 完整的训练和推理工具链

## 🚀 快速开始

### 环境准备

```bash
# 激活conda环境
conda activate caby_train

# 安装依赖
pip install -r requirements.txt
```

### 训练模型

```bash
# 一键启动训练
bash run_training.sh
```

### 预测使用

```bash
# 单张图片预测
python quick_predict_calibrated.py image.jpg --with-confidence

# 批量预测
python quick_predict_calibrated.py /path/to/images/ --batch --with-confidence

# 完整数据集验证
python quick_predict_calibrated.py --validate --verbose
```

## 📊 性能指标

| 指标 | 值 |
|------|-----|
| 总体准确率 | 99.54% |
| 小花准确率 | 99.61% |
| 小黑准确率 | 100.00% |
| 小白准确率 | 99.17% |
| 推理速度 | 80+ images/s |
| 平均置信度 | 98.44% |
| 高置信度比例 | 98.88% (≥90%) |

## 📁 项目结构

```
├── 🧠 核心模块
│   ├── datasets/          # 数据集处理
│   ├── models/            # 模型定义
│   └── utils/             # 训练工具
├── 🚀 训练脚本
│   ├── train_cat_reid.py  # 主训练脚本
│   └── run_training.sh    # 训练启动脚本
├── 🔍 测试与预测
│   ├── test_model.py      # 模型测试
│   ├── quick_predict_calibrated.py  # 快速预测工具
│   ├── predict_calibrated.py       # 校准预测类
│   └── calibrate_confidence.py     # 置信度校准
├── 📊 实验结果
│   ├── experiments/       # 训练结果
│   └── test_results_final/ # 测试结果
└── 📚 文档
    ├── README.md          # 本文件
    ├── PROJECT_STRUCTURE.md  # 详细项目结构
    └── QUICK_PREDICT_USAGE.md # 预测工具使用说明
```

## 🛠️ 技术栈

- **深度学习框架**: PyTorch 2.7.1+cu126
- **预训练模型**: WildFusion MegaDescriptor-T-224
- **数据处理**: PIL, torchvision
- **训练监控**: WandB
- **评估指标**: scikit-learn
- **可视化**: matplotlib

## 📋 详细使用说明

### 1. 训练新模型

修改配置文件 `configs/cat_reid_config.yaml` 后运行：

```bash
bash run_training.sh
```

### 2. 模型测试

```bash
python test_model.py
```

### 3. 置信度校准

```bash
python calibrate_confidence.py --model experiments/megadescriptor_3cats_stable/best_model.pth
```

### 4. 批量预测示例

```bash
# 预测目录下所有图片并保存结果
python quick_predict_calibrated.py /path/to/images/ --batch -o results.json

# 验证数据集并输出错误案例
python quick_predict_calibrated.py --validate -o validation.json
```

## 📈 模型架构

- **基础模型**: MegaDescriptor-T-224 (Swin-T + MegaDescriptor head)
- **特征维度**: 768维
- **分类层**: 全连接层 + 温度缩放
- **损失函数**: 交叉熵损失 + Triplet损失
- **数据增强**: 随机裁剪、翻转、颜色抖动、旋转、擦除

## 🔧 配置说明

主要配置参数（`configs/cat_reid_config.yaml`）：

```yaml
model:
  name: "megadescriptor"
  temperature: 10.0

training:
  epochs: 100
  batch_size: 32
  learning_rate: 1e-5
  
loss:
  classification_weight: 0.01
  triplet_weight: 0.5
```

## 📚 文档链接

- [详细项目结构](PROJECT_STRUCTURE.md)
- [预测工具使用说明](QUICK_PREDICT_USAGE.md)
- [置信度校准说明](CONFIDENCE_CALIBRATION_SUMMARY.md)

## 🤝 贡献

欢迎提交Issues和Pull Requests来改进项目。

## 📄 许可证

本项目基于MIT许可证开源。 