#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F
import timm
from typing import Optional, Dict, Any
import logging
import matplotlib
import matplotlib.pyplot as plt

# 为中文字体设置
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'Source Han Sans CN', 'WenQuanYi Zen Hei', 'DejaVu Sans', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class CatReidModel(nn.Module):
    """基于MegaDescriptor的猫个体识别模型"""
    
    def __init__(
        self,
        num_classes: int = 3,
        backbone: str = "hf-hub:BVRA/MegaDescriptor-T-224",
        feature_dim: int = 768,
        dropout: float = 0.1,
        pretrained: bool = True,
        freeze_backbone: bool = False
    ):
        """
        Args:
            num_classes: 猫个体数量
            backbone: 骨干网络名称
            feature_dim: 特征维度
            dropout: Dropout比例
            pretrained: 是否使用预训练权重
            freeze_backbone: 是否冻结骨干网络
        """
        super(CatReidModel, self).__init__()
        
        self.num_classes = num_classes
        self.feature_dim = feature_dim
        
        # 加载预训练的MegaDescriptor
        self.backbone = timm.create_model(
            backbone, 
            pretrained=pretrained,
            num_classes=0  # 移除分类头
        )
        
        # 获取backbone输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 特征投影层
        self.feature_projector = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        # 分类器头部
        self.classifier = nn.Linear(feature_dim, num_classes)
        # 温度参数，用于缩放logits
        self.temperature = nn.Parameter(torch.ones(1) * 10.0)  # 初始温度为10，降低logits尺度
        
        # 初始化分类器权重为更小的值，避免初始logits过大
        nn.init.normal_(self.classifier.weight, std=0.01)
        nn.init.constant_(self.classifier.bias, 0)
        
        # 是否冻结骨干网络
        if freeze_backbone:
            self._freeze_backbone()
        
        self._init_weights()
        
        logger.info(f"CatReidModel初始化完成:")
        logger.info(f"  - 骨干网络: {backbone}")
        logger.info(f"  - 猫个体数: {num_classes}")
        logger.info(f"  - 特征维度: {feature_dim}")
        logger.info(f"  - 骨干网络输出维度: {backbone_dim}")
        logger.info(f"  - 冻结骨干网络: {freeze_backbone}")
    
    def _freeze_backbone(self):
        """冻结骨干网络参数"""
        for param in self.backbone.parameters():
            param.requires_grad = False
        logger.info("骨干网络参数已冻结")
    
    def _init_weights(self):
        """初始化权重"""
        for m in [self.feature_projector, self.classifier]:
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x: torch.Tensor, return_features: bool = False):
        """
        前向传播
        
        Args:
            x: 输入图像 (B, 3, H, W)
            return_features: 是否返回特征向量
            
        Returns:
            如果return_features=True: (features, logits)
            否则: logits
        """
        # 骨干网络特征提取
        backbone_features = self.backbone(x)  # (B, backbone_dim)
        
        # 特征投影
        features = self.feature_projector(backbone_features)  # (B, feature_dim)
        
        # L2归一化特征
        features = F.normalize(features, p=2, dim=1)
        
        # 分类
        logits = self.classifier(features)  # (B, num_classes)
        # 应用温度缩放
        logits = logits / self.temperature
        
        if return_features:
            return features, logits
        else:
            return logits
    
    def extract_features(self, x: torch.Tensor) -> torch.Tensor:
        """提取特征向量（用于相似度计算）"""
        with torch.no_grad():
            backbone_features = self.backbone(x)
            features = self.feature_projector(backbone_features)
            # L2归一化
            features = F.normalize(features, p=2, dim=1)
        return features

class TripletLoss(nn.Module):
    """改进的Triplet Loss损失函数 - 修复数值稳定性问题"""
    
    def __init__(self, margin: float = 0.3, mining: str = 'batch_hard', eps: float = 1e-8):
        """
        Args:
            margin: 间隔大小
            mining: 挖掘策略 ('batch_all', 'batch_hard')
            eps: 数值稳定性参数
        """
        super(TripletLoss, self).__init__()
        self.margin = margin
        self.mining = mining
        self.eps = eps
        
    def forward(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        计算Triplet Loss
        
        Args:
            features: 特征向量 (B, D)
            labels: 标签 (B,)
            
        Returns:
            loss: Triplet Loss值
        """
        # 确保特征是归一化的
        features = F.normalize(features, p=2, dim=1, eps=self.eps)
        
        if self.mining == 'batch_all':
            return self._batch_all_triplet_loss(features, labels)
        elif self.mining == 'batch_hard':
            return self._batch_hard_triplet_loss(features, labels)
        else:
            raise ValueError(f"Unknown mining strategy: {self.mining}")
    
    def _batch_all_triplet_loss(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """Batch All策略的Triplet Loss - 改进版本"""
        batch_size = features.size(0)
        
        # 计算距离矩阵 - 使用欧几里得距离
        dist_matrix = torch.cdist(features, features, p=2).clamp(min=self.eps)
        
        # 创建标签匹配矩阵
        labels_equal = labels.unsqueeze(1) == labels.unsqueeze(0)
        
        # positive mask: 相同标签且不是自己
        positive_mask = labels_equal & ~torch.eye(batch_size, dtype=torch.bool, device=features.device)
        
        # negative mask: 不同标签
        negative_mask = ~labels_equal
        
        # 计算有效的triplet loss
        triplet_losses = []
        
        for i in range(batch_size):
            # 获取positive和negative距离
            pos_dists = dist_matrix[i][positive_mask[i]]
            neg_dists = dist_matrix[i][negative_mask[i]]
            
            if len(pos_dists) > 0 and len(neg_dists) > 0:
                # 计算所有positive-negative组合的loss
                pos_dists = pos_dists.unsqueeze(1)  # (num_pos, 1)
                neg_dists = neg_dists.unsqueeze(0)  # (1, num_neg)
                
                # 广播计算所有组合
                losses = F.relu(pos_dists - neg_dists + self.margin)
                triplet_losses.append(losses.mean())
        
        if triplet_losses:
            return torch.stack(triplet_losses).mean()
        else:
            # 如果没有有效的triplet，返回小的正值而不是0
            return torch.tensor(self.margin * 0.1, device=features.device, requires_grad=True)
    
    def _batch_hard_triplet_loss(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """Batch Hard策略的Triplet Loss - 改进版本"""
        batch_size = features.size(0)
        
        # 计算距离矩阵
        dist_matrix = torch.cdist(features, features, p=2).clamp(min=self.eps)
        
        # 创建标签匹配矩阵
        labels_equal = labels.unsqueeze(1) == labels.unsqueeze(0)
        
        # positive mask: 相同标签且不是自己
        positive_mask = labels_equal & ~torch.eye(batch_size, dtype=torch.bool, device=features.device)
        
        # negative mask: 不同标签
        negative_mask = ~labels_equal
        
        triplet_losses = []
        
        for i in range(batch_size):
            # 找到最难的positive（距离最远的同类样本）
            pos_dists = dist_matrix[i][positive_mask[i]]
            if len(pos_dists) > 0:
                hard_positive_dist = pos_dists.max()
            else:
                # 如果没有positive样本，跳过
                continue
            
            # 找到最难的negative（距离最近的异类样本）
            neg_dists = dist_matrix[i][negative_mask[i]]
            if len(neg_dists) > 0:
                hard_negative_dist = neg_dists.min()
            else:
                # 如果没有negative样本，跳过
                continue
            
            # 计算triplet loss
            loss = F.relu(hard_positive_dist - hard_negative_dist + self.margin)
            triplet_losses.append(loss)
        
        if triplet_losses:
            avg_loss = torch.stack(triplet_losses).mean()
            # 确保loss不为0（加入小的正则化项）
            return avg_loss + self.eps
        else:
            # 如果没有有效的triplet，返回小的正值
            return torch.tensor(self.margin * 0.1, device=features.device, requires_grad=True)

class CombinedLoss(nn.Module):
    """组合损失函数：分类损失 + Triplet损失 - 改进版本"""
    
    def __init__(
        self, 
        classification_weight: float = 1.0, 
        triplet_weight: float = 1.0,
        triplet_margin: float = 0.3,
        triplet_mining: str = 'batch_hard',
        label_smoothing: float = 0.1
    ):
        super(CombinedLoss, self).__init__()
        self.classification_weight = classification_weight
        self.triplet_weight = triplet_weight
        
        # 使用标签平滑的交叉熵损失
        self.classification_loss = nn.CrossEntropyLoss(label_smoothing=label_smoothing)
        self.triplet_loss = TripletLoss(margin=triplet_margin, mining=triplet_mining)
        
    def forward(self, features: torch.Tensor, logits: torch.Tensor, labels: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算组合损失
        
        Args:
            features: 特征向量 (B, D)
            logits: 分类输出 (B, C)
            labels: 真实标签 (B,)
            
        Returns:
            loss_dict: 包含各种损失的字典
        """
        # 分类损失
        cls_loss = self.classification_loss(logits, labels)
        
        # Triplet损失
        triplet_loss = self.triplet_loss(features, labels)
        
        # 总损失
        total_loss = (self.classification_weight * cls_loss + 
                     self.triplet_weight * triplet_loss)
        
        return {
            'total_loss': total_loss,
            'classification_loss': cls_loss,
            'triplet_loss': triplet_loss
        }

def create_model(num_classes: int, **kwargs) -> CatReidModel:
    """创建模型的工厂函数"""
    return CatReidModel(num_classes=num_classes, **kwargs)

if __name__ == "__main__":
    # 测试模型
    model = CatReidModel(num_classes=3)
    
    # 测试前向传播
    x = torch.randn(4, 3, 224, 224)
    features, logits = model(x, return_features=True)
    
    print(f"输入形状: {x.shape}")
    print(f"特征形状: {features.shape}")
    print(f"分类输出形状: {logits.shape}")
    
    # 测试损失函数
    labels = torch.randint(0, 3, (4,))
    loss_fn = CombinedLoss()
    loss_dict = loss_fn(features, logits, labels)
    
    print(f"损失字典: {loss_dict}")
    
    print("模型测试完成！") 