#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
校准模型预测脚本
支持使用校准后的模型进行高置信度预测
"""

import sys
import argparse
from pathlib import Path
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from predict_single import CatPredictor
from models.cat_reid_model import CatReidModel

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CalibratedCatPredictor(CatPredictor):
    """校准后的猫预测器"""
    
    def load_model(self):
        """加载校准后的模型"""
        logger.info(f"加载校准模型: {self.model_path}")
        
        # 加载检查点
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=True)
        except Exception:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 检查是否为校准模型
        if 'temperature' in checkpoint:
            logger.info("检测到校准模型，加载校准参数...")
            
            # 获取模型配置
            if 'model_config' in checkpoint:
                model_config = checkpoint['model_config']
                num_classes = model_config['num_classes']
                feature_dim = model_config['feature_dim']
            else:
                num_classes = 3
                feature_dim = 768
            
            # 创建基础模型
            base_model = CatReidModel(num_classes=num_classes, feature_dim=feature_dim)
            base_model.load_state_dict(checkpoint['model_state_dict'])
            
            # 创建校准后的模型
            self.model = CalibratedModel(
                base_model, 
                temperature=checkpoint['temperature'],
                feature_scaler=checkpoint.get('feature_scaler', None)
            )
            
            # 打印校准信息
            if 'calibration_results' in checkpoint:
                results = checkpoint['calibration_results']
                logger.info(f"校准结果:")
                logger.info(f"  - 原始置信度: {results['before_avg_confidence']:.4f}")
                logger.info(f"  - 校准后置信度: {results['after_avg_confidence']:.4f}")
                logger.info(f"  - 提升幅度: {results['improvement']:.4f}")
                logger.info(f"  - 准确率: {results['accuracy']:.4f}")
        else:
            # 普通模型
            logger.info("加载普通模型...")
            super().load_model()
            return
            
        self.model.to(self.device)
        self.model.eval()
        
        # 设置类别映射
        self.id_to_cat = {0: '小白', 1: '小花', 2: '小黑'}
        
        logger.info(f"校准模型加载完成")
        
        # 打印模型信息
        if 'epoch' in checkpoint:
            logger.info(f"训练轮数: {checkpoint['epoch']}")
        if 'best_val_acc' in checkpoint:
            logger.info(f"最佳验证准确率: {checkpoint['best_val_acc']:.4f}")

class CalibratedModel(nn.Module):
    """校准后的模型包装器"""
    
    def __init__(self, base_model, temperature=1.0, feature_scaler=None):
        super().__init__()
        self.base_model = base_model
        self.temperature = temperature
        
        if feature_scaler is not None:
            self.feature_scaler = nn.Parameter(feature_scaler)
            self.use_feature_scaling = True
        else:
            self.use_feature_scaling = False
    
    def forward(self, x, return_features=False):
        # 获取基础特征和logits
        features, logits = self.base_model(x, return_features=True)
        
        # 可选的特征缩放
        if self.use_feature_scaling:
            features = features * self.feature_scaler
            features = F.normalize(features, p=2, dim=1)
            # 重新计算logits
            logits = self.base_model.classifier(features)
        
        # 温度缩放
        scaled_logits = logits / self.temperature
        
        if return_features:
            return features, scaled_logits
        return scaled_logits

def main():
    parser = argparse.ArgumentParser(description="校准模型预测")
    parser.add_argument("image", help="输入图片路径")
    parser.add_argument("--model", "-m", 
                       default="experiments/megadescriptor_3cats_stable/best_model_calibrated.pth",
                       help="校准后的模型路径")
    parser.add_argument("--device", "-d", default="cuda", help="设备 (cuda/cpu)")
    parser.add_argument("--no-viz", action="store_true", help="不显示可视化结果")
    parser.add_argument("--save", "-s", action="store_true", help="保存预测结果图")
    parser.add_argument("--compare", action="store_true", help="与原始模型对比")
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not Path(args.model).exists():
        logger.error(f"校准模型文件不存在: {args.model}")
        # 尝试原始模型
        original_model = args.model.replace('_calibrated.pth', '.pth')
        if Path(original_model).exists():
            logger.info(f"使用原始模型: {original_model}")
            args.model = original_model
        else:
            return
    
    # 创建校准预测器
    predictor = CalibratedCatPredictor(args.model, args.device)
    
    try:
        # 加载模型
        predictor.load_model()
        predictor.setup_transform()
        
        # 检查图片文件
        if not Path(args.image).exists():
            logger.error(f"图片文件不存在: {args.image}")
            return
        
        # 进行预测
        result = predictor.predict(
            args.image, 
            show_confidence=not args.no_viz,
            save_result=args.save
        )
        
        # 对比模式
        if args.compare:
            logger.info("\n=== 与原始模型对比 ===")
            
            # 尝试加载原始模型
            original_model_path = args.model.replace('_calibrated.pth', '.pth')
            if Path(original_model_path).exists():
                original_predictor = CatPredictor(original_model_path, args.device)
                original_predictor.load_model()
                original_predictor.setup_transform()
                
                original_result = original_predictor.predict(
                    args.image, 
                    show_confidence=False,
                    save_result=False
                )
                
                logger.info(f"原始模型:")
                logger.info(f"  预测: {original_result['predicted_cat']}")
                logger.info(f"  置信度: {original_result['confidence']:.4f}")
                
                logger.info(f"校准模型:")
                logger.info(f"  预测: {result['predicted_cat']}")
                logger.info(f"  置信度: {result['confidence']:.4f}")
                
                improvement = result['confidence'] - original_result['confidence']
                logger.info(f"置信度提升: {improvement:+.4f}")
                
                if result['predicted_cat'] == original_result['predicted_cat']:
                    logger.info("✅ 预测结果一致")
                else:
                    logger.warning("⚠️ 预测结果不一致")
            else:
                logger.warning("原始模型不存在，无法对比")
        
        logger.info("✅ 预测完成")
        
    except Exception as e:
        logger.error(f"❌ 预测失败: {e}")
        raise

if __name__ == "__main__":
    main() 