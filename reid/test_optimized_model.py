#!/usr/bin/env python3
"""
优化模型测试脚本
测试困难样本优化后的模型性能
"""

import torch
import torch.nn.functional as F
import json
import numpy as np
from pathlib import Path
import logging
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import random
import time
from typing import Dict, List, Tuple, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model(model_path: str, device):
    """加载模型"""
    from feature_based_cat_recognition import FeatureExtractorModel

    try:
        from optimize_difficult_samples import EnhancedFeatureExtractor
        enhanced_available = True
    except ImportError:
        enhanced_available = False

    checkpoint = torch.load(model_path, map_location=device)

    # 判断模型类型
    if enhanced_available and 'model_type' in checkpoint and checkpoint['model_type'] == 'EnhancedFeatureExtractor':
        model = EnhancedFeatureExtractor(feature_dim=2048, num_classes=3)
        logger.info("加载增强特征提取器")
    else:
        model = FeatureExtractorModel(feature_dim=2048)
        logger.info("加载标准特征提取器")

    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model.to(device)

    return model

class OptimizedModelTester:
    """优化模型测试器"""
    
    def __init__(self, model, device, transform):
        self.model = model
        self.device = device
        self.transform = transform
        
    def extract_features(self, image_path: str) -> torch.Tensor:
        """提取单张图片的特征"""
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.transform(image).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            if hasattr(self.model, 'forward'):
                # 检查forward方法的参数数量
                import inspect
                sig = inspect.signature(self.model.forward)
                if len(sig.parameters) > 2:  # self, x, class_hint
                    features, _ = self.model(image_tensor)
                else:
                    features = self.model(image_tensor)
            else:
                features = self.model(image_tensor)
        
        return features.squeeze(0)
    
    def build_reference_features(self, annotations_path: str, images_dir: str, 
                               samples_per_class: int = 50) -> Dict[str, torch.Tensor]:
        """构建参考特征库"""
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        images_dir = Path(images_dir)
        reference_features = defaultdict(list)
        
        # 按类别收集图片
        class_images = defaultdict(list)
        for img_path, label in annotations.items():
            full_path = images_dir / Path(img_path).name
            if full_path.exists():
                class_images[label].append(str(full_path))
        
        # 为每个类别提取特征
        for class_name, image_paths in class_images.items():
            logger.info(f"为类别 {class_name} 提取参考特征...")
            
            # 随机选择样本
            if len(image_paths) > samples_per_class:
                selected_paths = random.sample(image_paths, samples_per_class)
            else:
                selected_paths = image_paths
            
            for img_path in selected_paths:
                try:
                    features = self.extract_features(img_path)
                    reference_features[class_name].append(features)
                except Exception as e:
                    logger.warning(f"无法处理图片 {img_path}: {e}")
        
        # 计算每个类别的平均特征
        averaged_features = {}
        for class_name, features_list in reference_features.items():
            if features_list:
                stacked_features = torch.stack(features_list)
                averaged_features[class_name] = torch.mean(stacked_features, dim=0)
                logger.info(f"类别 {class_name}: {len(features_list)} 个参考样本")
        
        return averaged_features
    
    def predict_single(self, image_path: str, reference_features: Dict[str, torch.Tensor]) -> Tuple[str, float, Dict[str, float]]:
        """预测单张图片"""
        query_features = self.extract_features(image_path)
        
        similarities = {}
        for class_name, ref_features in reference_features.items():
            similarity = F.cosine_similarity(query_features.unsqueeze(0), ref_features.unsqueeze(0)).item()
            similarities[class_name] = similarity
        
        # 找到最高相似度
        best_class = max(similarities, key=similarities.get)
        best_similarity = similarities[best_class]
        
        return best_class, best_similarity, similarities
    
    def comprehensive_test(self, annotations_path: str, images_dir: str, 
                          reference_features: Dict[str, torch.Tensor], 
                          num_rounds: int = 5, samples_per_round: int = 300) -> Dict:
        """综合测试"""
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        images_dir = Path(images_dir)
        
        # 准备测试数据
        test_data = []
        for img_path, true_label in annotations.items():
            full_path = images_dir / Path(img_path).name
            if full_path.exists():
                test_data.append((str(full_path), true_label))
        
        results = {
            'num_rounds': num_rounds,
            'individual_results': [],
            'overall_stats': {}
        }
        
        all_accuracies = []
        all_throughputs = []
        category_stats = defaultdict(lambda: {'correct': 0, 'total': 0})
        all_errors = []
        
        for round_num in range(1, num_rounds + 1):
            logger.info(f"开始第 {round_num} 轮测试...")
            
            # 随机选择测试样本
            round_samples = random.sample(test_data, min(samples_per_round, len(test_data)))
            
            correct_predictions = 0
            total_predictions = len(round_samples)
            detailed_results = []
            
            # 计算吞吐量
            start_time = time.time()
            
            for img_path, true_label in round_samples:
                try:
                    pred_label, best_similarity, similarities = self.predict_single(img_path, reference_features)
                    
                    is_correct = (pred_label == true_label)
                    if is_correct:
                        correct_predictions += 1
                    else:
                        all_errors.append({
                            'round': round_num,
                            'image_path': img_path,
                            'true_category': true_label,
                            'predicted_category': pred_label,
                            'best_similarity': best_similarity,
                            'similarities': similarities
                        })
                    
                    # 更新类别统计
                    category_stats[true_label]['total'] += 1
                    if is_correct:
                        category_stats[true_label]['correct'] += 1
                    
                    detailed_results.append({
                        'image_path': img_path,
                        'true_category': true_label,
                        'predicted_category': pred_label,
                        'best_similarity': f"{best_similarity:.7f}",
                        'similarities': {k: f"{v:.7f}" for k, v in similarities.items()},
                        'is_correct': is_correct
                    })
                    
                except Exception as e:
                    logger.warning(f"预测失败 {img_path}: {e}")
                    total_predictions -= 1
            
            end_time = time.time()
            
            # 计算指标
            if total_predictions > 0:
                accuracy = correct_predictions / total_predictions
                throughput = total_predictions / (end_time - start_time)
                
                all_accuracies.append(accuracy)
                all_throughputs.append(throughput)
                
                round_result = {
                    'round': round_num,
                    'total_tested': total_predictions,
                    'correct_predictions': correct_predictions,
                    'accuracy': accuracy,
                    'throughput_fps': throughput,
                    'detailed_results': detailed_results
                }
                
                results['individual_results'].append(round_result)
                
                logger.info(f"第 {round_num} 轮完成: 准确率 {accuracy:.3f}, 吞吐量 {throughput:.1f} fps")
        
        # 计算总体统计
        if all_accuracies:
            overall_accuracy = np.mean(all_accuracies)
            accuracy_std = np.std(all_accuracies)
            overall_throughput = np.mean(all_throughputs)
            throughput_std = np.std(all_throughputs)
            
            # 计算各类别准确率
            category_accuracies = {}
            for category, stats in category_stats.items():
                if stats['total'] > 0:
                    category_accuracies[category] = stats['correct'] / stats['total']
            
            results['overall_stats'] = {
                'average_accuracy': overall_accuracy,
                'accuracy_std': accuracy_std,
                'average_throughput': overall_throughput,
                'throughput_std': throughput_std,
                'category_accuracies': category_accuracies,
                'total_errors': len(all_errors),
                'error_rate': len(all_errors) / (num_rounds * samples_per_round)
            }
            
            logger.info(f"总体结果:")
            logger.info(f"  平均准确率: {overall_accuracy:.3f} ± {accuracy_std:.3f}")
            logger.info(f"  平均吞吐量: {overall_throughput:.1f} ± {throughput_std:.1f} fps")
            logger.info(f"  各类别准确率: {category_accuracies}")
            logger.info(f"  总错误数: {len(all_errors)}")
        
        return results

def compare_models():
    """比较原始模型和优化模型的性能"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据变换
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 数据路径
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    images_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    
    models_to_test = []
    
    # 原始模型
    if Path("feature_extractor_model.pth").exists():
        models_to_test.append(("原始模型", "feature_extractor_model.pth"))
    
    # 优化模型
    if Path("feature_extractor_model_optimized.pth").exists():
        models_to_test.append(("优化模型", "feature_extractor_model_optimized.pth"))
    
    if not models_to_test:
        logger.error("没有找到可测试的模型!")
        return
    
    comparison_results = {}
    
    for model_name, model_path in models_to_test:
        logger.info(f"测试 {model_name}...")
        
        # 加载模型
        model = load_model(model_path, device)
        tester = OptimizedModelTester(model, device, transform)
        
        # 构建参考特征
        reference_features = tester.build_reference_features(annotations_path, images_dir, samples_per_class=50)
        
        # 综合测试
        results = tester.comprehensive_test(annotations_path, images_dir, reference_features, 
                                          num_rounds=3, samples_per_round=200)
        
        comparison_results[model_name] = results
        
        # 保存结果
        result_file = f"test_results_{model_name.replace('模型', '_model')}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"{model_name} 测试完成，结果保存到 {result_file}")
    
    # 比较结果
    if len(comparison_results) > 1:
        logger.info("\n" + "="*60)
        logger.info("模型性能比较")
        logger.info("="*60)
        
        for model_name, results in comparison_results.items():
            stats = results['overall_stats']
            logger.info(f"{model_name}:")
            logger.info(f"  准确率: {stats['average_accuracy']:.3f} ± {stats['accuracy_std']:.3f}")
            logger.info(f"  吞吐量: {stats['average_throughput']:.1f} ± {stats['throughput_std']:.1f} fps")
            logger.info(f"  错误率: {stats['error_rate']:.3f}")
            logger.info(f"  各类别准确率: {stats['category_accuracies']}")
            logger.info("")

def main():
    """主函数"""
    compare_models()

if __name__ == "__main__":
    main()
