#!/usr/bin/env python3
"""
阈值优化和集成预测脚本
通过优化相似度阈值和集成多个模型来提高识别准确率
"""

import torch
import torch.nn.functional as F
import json
import numpy as np
from pathlib import Path
import logging
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import random
from typing import Dict, List, Tuple, Optional
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
import matplotlib.pyplot as plt

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ThresholdOptimizer:
    """相似度阈值优化器"""
    
    def __init__(self, model, device, transform):
        self.model = model
        self.device = device
        self.transform = transform
        self.optimal_thresholds = {}
        
    def extract_features(self, image_path: str) -> torch.Tensor:
        """提取单张图片的特征"""
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.transform(image).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            if hasattr(self.model, 'forward'):
                # 新的增强模型
                features, _ = self.model(image_tensor)
            else:
                # 原始模型
                features = self.model(image_tensor)
        
        return features.squeeze(0)
    
    def build_reference_features(self, annotations_path: str, images_dir: str, 
                               samples_per_class: int = 50) -> Dict[str, torch.Tensor]:
        """构建参考特征库，增加样本数量"""
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        images_dir = Path(images_dir)
        reference_features = defaultdict(list)
        
        # 按类别收集图片
        class_images = defaultdict(list)
        for img_path, label in annotations.items():
            full_path = images_dir / Path(img_path).name
            if full_path.exists():
                class_images[label].append(str(full_path))
        
        # 为每个类别提取特征
        for class_name, image_paths in class_images.items():
            logger.info(f"为类别 {class_name} 提取特征...")
            
            # 随机选择样本（如果图片数量超过指定数量）
            if len(image_paths) > samples_per_class:
                selected_paths = random.sample(image_paths, samples_per_class)
            else:
                selected_paths = image_paths
            
            for img_path in selected_paths:
                try:
                    features = self.extract_features(img_path)
                    reference_features[class_name].append(features)
                except Exception as e:
                    logger.warning(f"无法处理图片 {img_path}: {e}")
        
        # 计算每个类别的平均特征
        averaged_features = {}
        for class_name, features_list in reference_features.items():
            if features_list:
                stacked_features = torch.stack(features_list)
                averaged_features[class_name] = torch.mean(stacked_features, dim=0)
                logger.info(f"类别 {class_name}: {len(features_list)} 个样本")
        
        return averaged_features
    
    def predict_with_threshold(self, image_path: str, reference_features: Dict[str, torch.Tensor], 
                             threshold: float = 0.7) -> Tuple[Optional[str], float, Dict[str, float]]:
        """使用阈值进行预测"""
        query_features = self.extract_features(image_path)
        
        similarities = {}
        for class_name, ref_features in reference_features.items():
            similarity = F.cosine_similarity(query_features.unsqueeze(0), ref_features.unsqueeze(0)).item()
            similarities[class_name] = similarity
        
        # 找到最高相似度
        best_class = max(similarities, key=similarities.get)
        best_similarity = similarities[best_class]
        
        # 应用阈值
        if best_similarity >= threshold:
            return best_class, best_similarity, similarities
        else:
            return None, best_similarity, similarities  # 拒绝预测
    
    def optimize_thresholds(self, annotations_path: str, images_dir: str, 
                          reference_features: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """优化每个类别的最佳阈值"""
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        images_dir = Path(images_dir)
        
        # 收集验证数据
        validation_data = []
        for img_path, true_label in annotations.items():
            full_path = images_dir / Path(img_path).name
            if full_path.exists():
                validation_data.append((str(full_path), true_label))
        
        # 随机选择验证集
        random.shuffle(validation_data)
        validation_data = validation_data[:300]  # 使用300个样本进行阈值优化
        
        # 测试不同阈值
        thresholds = np.arange(0.5, 0.95, 0.05)
        best_threshold = 0.7
        best_accuracy = 0
        
        threshold_results = []
        
        for threshold in thresholds:
            correct = 0
            total = 0
            rejected = 0
            
            for img_path, true_label in validation_data:
                try:
                    pred_label, similarity, similarities = self.predict_with_threshold(
                        img_path, reference_features, threshold
                    )
                    
                    if pred_label is not None:
                        if pred_label == true_label:
                            correct += 1
                        total += 1
                    else:
                        rejected += 1
                        
                except Exception as e:
                    logger.warning(f"预测失败 {img_path}: {e}")
            
            if total > 0:
                accuracy = correct / total
                rejection_rate = rejected / len(validation_data)
                
                threshold_results.append({
                    'threshold': threshold,
                    'accuracy': accuracy,
                    'rejection_rate': rejection_rate,
                    'total_predictions': total
                })
                
                logger.info(f"阈值 {threshold:.2f}: 准确率 {accuracy:.3f}, 拒绝率 {rejection_rate:.3f}")
                
                # 选择准确率最高且拒绝率合理的阈值
                if accuracy > best_accuracy and rejection_rate < 0.3:
                    best_accuracy = accuracy
                    best_threshold = threshold
        
        logger.info(f"最佳阈值: {best_threshold:.2f}, 准确率: {best_accuracy:.3f}")
        
        # 保存阈值优化结果
        with open('threshold_optimization_results.json', 'w', encoding='utf-8') as f:
            json.dump({
                'best_threshold': best_threshold,
                'best_accuracy': best_accuracy,
                'all_results': threshold_results
            }, f, indent=2, ensure_ascii=False)
        
        return {'global': best_threshold}

class EnsemblePredictor:
    """集成预测器"""
    
    def __init__(self, models: List[torch.nn.Module], device, transform):
        self.models = models
        self.device = device
        self.transform = transform
        
    def predict_ensemble(self, image_path: str, reference_features_list: List[Dict[str, torch.Tensor]], 
                        threshold: float = 0.7) -> Tuple[Optional[str], float, Dict[str, float]]:
        """集成多个模型的预测结果"""
        all_similarities = defaultdict(list)
        
        for i, model in enumerate(self.models):
            # 提取特征
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                if hasattr(model, 'forward') and len(model.forward.__code__.co_varnames) > 2:
                    features, _ = model(image_tensor)
                else:
                    features = model(image_tensor)
            
            query_features = features.squeeze(0)
            reference_features = reference_features_list[i]
            
            # 计算相似度
            for class_name, ref_features in reference_features.items():
                similarity = F.cosine_similarity(query_features.unsqueeze(0), ref_features.unsqueeze(0)).item()
                all_similarities[class_name].append(similarity)
        
        # 集成相似度（平均）
        ensemble_similarities = {}
        for class_name, similarities in all_similarities.items():
            ensemble_similarities[class_name] = np.mean(similarities)
        
        # 预测
        best_class = max(ensemble_similarities, key=ensemble_similarities.get)
        best_similarity = ensemble_similarities[best_class]
        
        if best_similarity >= threshold:
            return best_class, best_similarity, ensemble_similarities
        else:
            return None, best_similarity, ensemble_similarities

def load_model(model_path: str, device):
    """加载模型"""
    from feature_based_cat_recognition import FeatureExtractorModel

    try:
        from optimize_difficult_samples import EnhancedFeatureExtractor
        enhanced_available = True
    except ImportError:
        enhanced_available = False

    checkpoint = torch.load(model_path, map_location=device)

    # 判断模型类型
    if enhanced_available and 'model_type' in checkpoint and checkpoint['model_type'] == 'EnhancedFeatureExtractor':
        model = EnhancedFeatureExtractor(feature_dim=2048, num_classes=3)
    else:
        model = FeatureExtractorModel(feature_dim=2048)

    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model.to(device)

    return model

def main():
    """主函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据变换
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 数据路径
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    images_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    
    # 加载模型
    models = []
    reference_features_list = []
    
    # 原始模型
    if Path("feature_extractor_model.pth").exists():
        logger.info("加载原始模型...")
        original_model = load_model("feature_extractor_model.pth", device)
        models.append(original_model)
        
        # 构建参考特征
        optimizer = ThresholdOptimizer(original_model, device, transform)
        ref_features = optimizer.build_reference_features(annotations_path, images_dir, samples_per_class=50)
        reference_features_list.append(ref_features)
    
    # 优化后的模型（如果存在）
    if Path("feature_extractor_model_optimized.pth").exists():
        logger.info("加载优化模型...")
        optimized_model = load_model("feature_extractor_model_optimized.pth", device)
        models.append(optimized_model)
        
        # 构建参考特征
        optimizer_opt = ThresholdOptimizer(optimized_model, device, transform)
        ref_features_opt = optimizer_opt.build_reference_features(annotations_path, images_dir, samples_per_class=50)
        reference_features_list.append(ref_features_opt)
    
    if not models:
        logger.error("没有找到可用的模型文件!")
        return
    
    # 阈值优化
    logger.info("开始阈值优化...")
    optimizer = ThresholdOptimizer(models[0], device, transform)
    optimal_thresholds = optimizer.optimize_thresholds(annotations_path, images_dir, reference_features_list[0])
    
    # 如果有多个模型，使用集成预测
    if len(models) > 1:
        logger.info("使用集成预测...")
        ensemble_predictor = EnsemblePredictor(models, device, transform)
        
        # 测试集成预测效果
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        test_samples = list(annotations.items())[:100]  # 测试100个样本
        correct = 0
        total = 0
        
        for img_path, true_label in test_samples:
            full_path = Path(images_dir) / Path(img_path).name
            if full_path.exists():
                try:
                    pred_label, similarity, similarities = ensemble_predictor.predict_ensemble(
                        str(full_path), reference_features_list, optimal_thresholds['global']
                    )
                    
                    if pred_label is not None:
                        if pred_label == true_label:
                            correct += 1
                        total += 1
                        
                except Exception as e:
                    logger.warning(f"集成预测失败 {full_path}: {e}")
        
        if total > 0:
            ensemble_accuracy = correct / total
            logger.info(f"集成预测准确率: {ensemble_accuracy:.3f}")
    
    logger.info("阈值优化完成!")

if __name__ == "__main__":
    main()
