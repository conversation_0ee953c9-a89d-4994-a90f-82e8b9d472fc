#!/usr/bin/env python3
"""
简化模型压缩脚本
实现基本的模型优化和格式转换
"""

import torch
import torch.nn as nn
import json
import numpy as np
from pathlib import Path
import logging
import time
from typing import Dict, List, Tuple

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model(model_path: str, device):
    """加载模型"""
    from feature_based_cat_recognition import FeatureExtractorModel
    
    checkpoint = torch.load(model_path, map_location=device)
    model = FeatureExtractorModel(feature_dim=2048)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model

class SimpleModelCompressor:
    """简化模型压缩器"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
    def create_torchscript_model(self, model_path: str) -> str:
        """创建TorchScript模型"""
        logger.info("创建TorchScript模型...")
        
        model = self.model.cpu()
        model.eval()
        
        # 创建示例输入
        dummy_input = torch.randn(1, 3, 224, 224)
        
        # 转换为TorchScript
        try:
            # 尝试trace方法
            traced_model = torch.jit.trace(model, dummy_input)
            script_path = model_path.replace('.pth', '_traced.pt')
            traced_model.save(script_path)
            logger.info(f"TorchScript traced模型保存到: {script_path}")
            return script_path
        except Exception as e:
            logger.warning(f"Trace方法失败: {e}")
            return None
    
    def create_half_precision_model(self, model_path: str) -> str:
        """创建半精度模型"""
        logger.info("创建半精度模型...")
        
        model = self.model.cpu()
        model.eval()
        
        # 转换为半精度
        model_half = model.half()
        
        # 保存半精度模型
        half_path = model_path.replace('.pth', '_half.pth')
        
        # 获取原始checkpoint信息
        original_checkpoint = torch.load(model_path, map_location='cpu')
        
        torch.save({
            'model_state_dict': model_half.state_dict(),
            'model_type': 'half_precision',
            'feature_dim': 2048,
            'compression_info': {
                'method': 'half_precision',
                'dtype': 'float16'
            },
            'training_history': original_checkpoint.get('training_history', {})
        }, half_path)
        
        logger.info(f"半精度模型保存到: {half_path}")
        return half_path
    
    def create_optimized_state_dict(self, model_path: str) -> str:
        """创建优化的状态字典（仅保存必要信息）"""
        logger.info("创建优化状态字典...")
        
        # 加载原始checkpoint
        original_checkpoint = torch.load(model_path, map_location='cpu')
        
        # 创建最小化的checkpoint
        optimized_checkpoint = {
            'model_state_dict': original_checkpoint['model_state_dict'],
            'feature_dim': original_checkpoint.get('feature_dim', 2048),
            'model_type': 'optimized'
        }
        
        # 保存优化版本
        optimized_path = model_path.replace('.pth', '_optimized.pth')
        torch.save(optimized_checkpoint, optimized_path)
        
        logger.info(f"优化状态字典保存到: {optimized_path}")
        return optimized_path

class ModelBenchmark:
    """模型性能基准测试"""
    
    def __init__(self, device):
        self.device = device
        
    def benchmark_pytorch_model(self, model, num_runs: int = 100) -> Dict:
        """基准测试PyTorch模型"""
        model = model.to(self.device)
        model.eval()
        
        # 预热
        dummy_input = torch.randn(1, 3, 224, 224).to(self.device)
        for _ in range(10):
            with torch.no_grad():
                _ = model(dummy_input)
        
        # 基准测试
        if self.device.type == 'cuda':
            torch.cuda.synchronize()
        
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_runs):
                _ = model(dummy_input)
        
        if self.device.type == 'cuda':
            torch.cuda.synchronize()
        
        end_time = time.time()
        
        avg_time = (end_time - start_time) / num_runs
        fps = 1.0 / avg_time
        
        return {
            'avg_inference_time': avg_time,
            'fps': fps,
            'device': str(self.device)
        }
    
    def benchmark_torchscript_model(self, script_path: str, num_runs: int = 100) -> Dict:
        """基准测试TorchScript模型"""
        try:
            # 加载TorchScript模型
            scripted_model = torch.jit.load(script_path, map_location=self.device)
            scripted_model.eval()
            
            # 准备输入
            dummy_input = torch.randn(1, 3, 224, 224).to(self.device)
            
            # 预热
            for _ in range(10):
                with torch.no_grad():
                    _ = scripted_model(dummy_input)
            
            # 基准测试
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
            
            start_time = time.time()
            
            with torch.no_grad():
                for _ in range(num_runs):
                    _ = scripted_model(dummy_input)
            
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
            
            end_time = time.time()
            
            avg_time = (end_time - start_time) / num_runs
            fps = 1.0 / avg_time
            
            return {
                'avg_inference_time': avg_time,
                'fps': fps,
                'device': str(self.device)
            }
        except Exception as e:
            logger.error(f"TorchScript基准测试失败: {e}")
            return None
    
    def get_model_size(self, model_path: str) -> Dict:
        """获取模型大小信息"""
        path = Path(model_path)
        if not path.exists():
            return None
        
        size_bytes = path.stat().st_size
        size_mb = size_bytes / (1024 * 1024)
        
        return {
            'size_bytes': size_bytes,
            'size_mb': round(size_mb, 2),
            'path': str(path)
        }

def compress_and_optimize():
    """压缩和优化模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    model_path = "feature_extractor_model.pth"
    
    if not Path(model_path).exists():
        logger.error(f"模型文件不存在: {model_path}")
        return
    
    # 加载原始模型
    logger.info("加载原始模型...")
    original_model = load_model(model_path, device)
    
    # 创建压缩器和基准测试器
    compressor = SimpleModelCompressor(original_model, device)
    benchmark = ModelBenchmark(device)
    
    results = {
        'original_model': {
            'path': model_path,
            'size_info': benchmark.get_model_size(model_path),
            'performance': benchmark.benchmark_pytorch_model(original_model)
        },
        'compressed_models': {}
    }
    
    logger.info("原始模型基准测试完成")
    
    # 1. TorchScript转换
    try:
        script_path = compressor.create_torchscript_model(model_path)
        if script_path:
            results['compressed_models']['torchscript'] = {
                'path': script_path,
                'size_info': benchmark.get_model_size(script_path),
                'performance': benchmark.benchmark_torchscript_model(script_path)
            }
            
            # 计算压缩比
            original_size = results['original_model']['size_info']['size_mb']
            script_size = results['compressed_models']['torchscript']['size_info']['size_mb']
            results['compressed_models']['torchscript']['compression_ratio'] = round(original_size / script_size, 2)
    except Exception as e:
        logger.error(f"TorchScript转换失败: {e}")
    
    # 2. 半精度模型
    try:
        half_path = compressor.create_half_precision_model(model_path)
        if half_path:
            # 加载半精度模型进行测试
            half_model = load_model(half_path, device)
            
            results['compressed_models']['half_precision'] = {
                'path': half_path,
                'size_info': benchmark.get_model_size(half_path),
                'performance': benchmark.benchmark_pytorch_model(half_model)
            }
            
            # 计算压缩比
            original_size = results['original_model']['size_info']['size_mb']
            half_size = results['compressed_models']['half_precision']['size_info']['size_mb']
            results['compressed_models']['half_precision']['compression_ratio'] = round(original_size / half_size, 2)
    except Exception as e:
        logger.error(f"半精度转换失败: {e}")
    
    # 3. 优化状态字典
    try:
        optimized_path = compressor.create_optimized_state_dict(model_path)
        if optimized_path:
            results['compressed_models']['optimized'] = {
                'path': optimized_path,
                'size_info': benchmark.get_model_size(optimized_path)
            }
            
            # 计算压缩比
            original_size = results['original_model']['size_info']['size_mb']
            optimized_size = results['compressed_models']['optimized']['size_info']['size_mb']
            results['compressed_models']['optimized']['compression_ratio'] = round(original_size / optimized_size, 2)
    except Exception as e:
        logger.error(f"状态字典优化失败: {e}")
    
    # 保存结果
    with open('simple_compression_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 输出总结
    logger.info("\n" + "="*60)
    logger.info("模型压缩结果总结")
    logger.info("="*60)
    
    original_size = results['original_model']['size_info']['size_mb']
    original_fps = results['original_model']['performance']['fps']
    
    logger.info(f"原始模型:")
    logger.info(f"  大小: {original_size} MB")
    logger.info(f"  性能: {original_fps:.1f} FPS")
    
    for model_type, info in results['compressed_models'].items():
        if info:
            size = info['size_info']['size_mb']
            ratio = info.get('compression_ratio', 'N/A')
            logger.info(f"{model_type.upper()}模型:")
            logger.info(f"  大小: {size} MB (压缩比: {ratio}x)")
            
            if 'performance' in info and info['performance']:
                fps = info['performance']['fps']
                speedup = fps / original_fps
                logger.info(f"  性能: {fps:.1f} FPS (加速比: {speedup:.2f}x)")
    
    logger.info("模型压缩完成!")
    
    # 推荐最佳部署格式
    logger.info("\n" + "="*60)
    logger.info("部署建议")
    logger.info("="*60)
    
    best_format = "原始模型"
    best_score = 0
    
    for model_type, info in results['compressed_models'].items():
        if info and 'performance' in info and info['performance']:
            # 综合评分：考虑压缩比和性能
            compression_score = info.get('compression_ratio', 1.0)
            performance_score = info['performance']['fps'] / original_fps
            combined_score = compression_score * 0.3 + performance_score * 0.7
            
            if combined_score > best_score:
                best_score = combined_score
                best_format = model_type
    
    logger.info(f"推荐部署格式: {best_format.upper()}")
    
    return results

if __name__ == "__main__":
    compress_and_optimize()
